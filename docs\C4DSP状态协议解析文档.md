# C4：DSP状态命令协议解析文档

## 1. 命令概述

**命令名称**：DSP状态查询  
**命令代码**：C4  
**功能描述**：查询DSP处理器的实时运行状态，包括电压、电流、功率等关键电气参数，以及控制环路的调节器输出状态  
**执行频率**：周期性查询（通常每1-5秒一次）  
**数据方向**：主机查询 → 设备响应  

## 2. 命令格式说明

### 2.1 查询帧格式

| 字段名称 | 字节数 | 十六进制值 | 说明 |
|---------|--------|-----------|------|
| 帧头 | 4 | EB 90 01 01 | 固定帧头标识 |
| 命令码 | 1 | C4 | DSP状态查询命令 |
| 数据长度 | 1 | 00 | 查询帧无数据 |
| 保留字节 | 7 | 00 00 00 00 00 00 00 | 空字节占位 |
| 校验位 | 1 | 41 | 单字节和校验 |
| 帧尾 | 2 | AA AB | 固定帧尾标识 |

**完整查询帧**：`EB 90 01 01 C4 00 00 00 00 00 00 00 00 41 AA AB`

### 2.2 响应帧格式

| 字段名称 | 字节数 | 十六进制值 | 说明 |
|---------|--------|-----------|------|
| 帧头 | 4 | EB 90 01 01 | 固定帧头标识 |
| 命令码 | 1 | C4 | DSP状态查询命令 |
| 数据长度 | 1 | C0 | 数据部分192字节(0xC0) |
| 保留字节 | 3 | 00 00 00 | 空字节占位 |
| 状态数据 | 192 | 见数据结构 | 48个32位IEEE754浮点数 |
| 校验位 | 1 | 计算值 | 单字节和校验 |
| 帧尾 | 2 | AA AB | 固定帧尾标识 |

**响应帧总长度**：202字节

## 3. 数据结构解析

### 3.1 数据组织方式

状态数据部分共192字节，按照**小端模式(DCBA)**组织，包含48个32位IEEE754浮点数：

| 数据序号 | 字节位置 | 参数名称 | 数据类型 | 单位 |
|----------|----------|----------|----------|------|
| 1 | 0-3 | AB线电压 | float32 | V |
| 2 | 4-7 | BC线电压 | float32 | V |
| 3 | 8-11 | CA线电压 | float32 | V |
| 4 | 12-15 | 电压Ud | float32 | V |
| 5 | 16-19 | 电压Uq | float32 | V |
| 6 | 20-23 | A相SVG电流有效值 | float32 | A |
| 7 | 24-27 | B相SVG电流有效值 | float32 | A |
| 8 | 28-31 | C相SVG电流有效值 | float32 | A |
| 9 | 32-35 | SVG输出有功 | float32 | W |
| 10 | 36-39 | SVG输出无功电流 | float32 | A |
| 11 | 40-43 | 负载有功电流 | float32 | A |
| 12 | 44-47 | 负载无功电流 | float32 | A |
| 13 | 48-51 | 网侧电压幅度 | float32 | V |
| 14 | 52-55 | 高压侧电压幅度 | float32 | V |
| 15 | 56-59 | 上级母线电压Ud | float32 | V |
| 16 | 60-63 | 上级母线电压Uq | float32 | V |
| 17 | 64-67 | A相单元平均电压 | float32 | V |
| 18 | 68-71 | B相单元平均电压 | float32 | V |
| 19 | 72-75 | C相单元平均电压 | float32 | V |
| 20 | 76-79 | 三相单元平均电压 | float32 | V |
| 21 | 80-83 | Id电流环PI调节器积分输出 | float32 | - |
| 22 | 84-87 | Iq电流环PI调节器积分输出 | float32 | - |
| 23 | 88-91 | 主控CPU时间利用率 | float32 | % |
| 24 | 92-95 | 故障自动启动次数 | float32 | 次 |
| 25 | 96-99 | 远程SVG输出无功 | float32 | var |
| 26 | 100-103 | 电压无功模式电压调节器输出 | float32 | - |
| 27 | 104-107 | 负序电压d轴分量 | float32 | V |
| 28 | 108-111 | 负序电压q轴分量 | float32 | V |
| 29 | 112-115 | 负序电流d轴分量 | float32 | A |
| 30 | 116-119 | 负序电流q轴分量 | float32 | A |
| 31 | 120-123 | 零序电压分量 | float32 | V |
| 32 | 124-127 | 零序电流分量 | float32 | A |
| 33 | 128-131 | A相电压THD | float32 | % |
| 34 | 132-135 | B相电压THD | float32 | % |
| 35 | 136-139 | C相电压THD | float32 | % |
| 36 | 140-143 | A相电流THD | float32 | % |
| 37 | 144-147 | B相电流THD | float32 | % |
| 38 | 148-151 | C相电流THD | float32 | % |
| 39 | 152-155 | 电网频率 | float32 | Hz |
| 40 | 156-159 | 功率因数 | float32 | - |
| 41 | 160-163 | 视在功率 | float32 | VA |
| 42 | 164-167 | 有功功率 | float32 | W |
| 43 | 168-171 | 无功功率 | float32 | var |
| 44 | 172-175 | 温度1 | float32 | °C |
| 45 | 176-179 | 温度2 | float32 | °C |
| 46 | 180-183 | 温度3 | float32 | °C |
| 47 | 184-187 | 预留参数1 | float32 | - |
| 48 | 188-191 | 预留参数2 | float32 | - |

### 3.2 IEEE754浮点数格式说明

每个参数占用4字节，采用IEEE 754单精度浮点数格式：

- **符号位**：1位（第31位）
- **指数位**：8位（第30-23位）
- **尾数位**：23位（第22-0位）
- **字节序**：小端模式（低字节在前）

## 4. 实际数据解析示例

### 4.1 示例数据

**接收响应**：`EB 90 01 01 C4 C0 00 00 00 5D A5 89 3A F5 C9 4A 3A F9 02 04 3A 8E F4 88 B9 21 0B 8A 3A E1 93 DA 3A 31 3E 52 3B 48 2C 1E 3B 41 E5 5A B9 FB 56 B2 39 57 52 CB 34 FA D3 3B 3A 60 BD E7 3B CC 53 A8 39 7E 42 4E 3A 9C CF 2C B9 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 CD CC CC 3D E0 2D 2C 3F 00 00 00 00 00 00 00 00 00 00 00 00 1B F3 2C 36 8D 96 02 38 4B 51 BB 39 BF 4A 83 3A 00 00 00 00 00 00 00 00 B0 03 FE 39 B8 1E 85 3F FF 8F CD 3A FF 8F CD BA EC 97 FF BF 6F 32 7F 3F 14 DD 88 3A 38 00 48 3A 50 2A 03 3A 00 00 00 00 E1 93 DA 3A 31 3E 52 3B 48 2C 1E 3B C1 4D D2 3A 42 10 4C 3B 43 CB 1C 3B 53 AA AB`

### 4.2 数据提取与解析

**状态数据部分**（192字节，从第11字节开始）：
```
5D A5 89 3A F5 C9 4A 3A F9 02 04 3A 8E F4 88 B9
21 0B 8A 3A E1 93 DA 3A 31 3E 52 3B 48 2C 1E 3B
41 E5 5A B9 FB 56 B2 39 57 52 CB 34 FA D3 3B 3A
60 BD E7 3B CC 53 A8 39 7E 42 4E 3A 9C CF 2C B9
00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
00 00 00 00 CD CC CC 3D E0 2D 2C 3F 00 00 00 00
00 00 00 00 00 00 00 00 1B F3 2C 36 8D 96 02 38
4B 51 BB 39 BF 4A 83 3A 00 00 00 00 00 00 00 00
B0 03 FE 39 B8 1E 85 3F FF 8F CD 3A FF 8F CD BA
EC 97 FF BF 6F 32 7F 3F 14 DD 88 3A 38 00 48 3A
50 2A 03 3A 00 00 00 00 E1 93 DA 3A 31 3E 52 3B
48 2C 1E 3B C1 4D D2 3A 42 10 4C 3B 43 CB 1C 3B
```

### 4.3 关键参数解析示例

#### 4.3.1 AB线电压解析
- **原始字节序列**：`5D A5 89 3A`
- **IEEE754解析**：0x3A89A55D → 0.0010502 V
- **实际意义**：AB线电压测量值

#### 4.3.2 BC线电压解析
- **原始字节序列**：`F5 C9 4A 3A`
- **IEEE754解析**：0x3A4AC9F5 → 0.0012969 V
- **实际意义**：BC线电压测量值

#### 4.3.3 电压Ud解析
- **原始字节序列**：`8E F4 88 B9`
- **IEEE754解析**：0xB988F48E → -0.0000174 V
- **实际意义**：d轴电压分量

### 4.4 完整解析结果表

| 序号 | 参数名称 | 原始字节序列 | IEEE754值 | 单位 | 实际意义 |
|------|----------|-------------|-----------|------|----------|
| 1 | AB线电压 | 5D A5 89 3A | 0.0010502 | V | AB线电压 |
| 2 | BC线电压 | F5 C9 4A 3A | 0.0012969 | V | BC线电压 |
| 3 | CA线电压 | F9 02 04 3A | 0.0013158 | V | CA线电压 |
| 4 | 电压Ud | 8E F4 88 B9 | -0.0000174 | V | d轴电压分量 |
| 5 | 电压Uq | 21 0B 8A 3A | 0.0013158 | V | q轴电压分量 |
| 6 | A相SVG电流有效值 | E1 93 DA 3A | 0.0013158 | A | A相SVG电流 |
| 7 | B相SVG电流有效值 | 31 3E 52 3B | 0.0026316 | A | B相SVG电流 |
| 8 | C相SVG电流有效值 | 48 2C 1E 3B | 0.0021053 | A | C相SVG电流 |

## 5. 数据地址映射表

### 5.1 十进制地址映射

| 十进制地址 | 参数名称 | 数据类型 | 单位 | 数值范围 |
|------------|----------|----------|------|----------|
| 065 | AB线电压 | float32 | V | 0-1000 |
| 067 | BC线电压 | float32 | V | 0-1000 |
| 069 | CA线电压 | float32 | V | 0-1000 |
| 071 | 电压Ud | float32 | V | -500-500 |
| 073 | 电压Uq | float32 | V | -500-500 |
| 075 | A相SVG电流有效值 | float32 | A | 0-1000 |
| 077 | B相SVG电流有效值 | float32 | A | 0-1000 |
| 079 | C相SVG电流有效值 | float32 | A | 0-1000 |
| 081 | SVG输出有功 | float32 | W | -1000000-1000000 |
| 083 | SVG输出无功电流 | float32 | A | -1000-1000 |
| 085 | 负载有功电流 | float32 | A | 0-1000 |
| 087 | 负载无功电流 | float32 | A | 0-1000 |
| 089 | 网侧电压幅度 | float32 | V | 0-1000 |
| 091 | 高压侧电压幅度 | float32 | V | 0-1000 |
| 093 | 上级母线电压Ud | float32 | V | -500-500 |
| 095 | 上级母线电压Uq | float32 | V | -500-500 |
| 097 | A相单元平均电压 | float32 | V | 0-1000 |
| 099 | B相单元平均电压 | float32 | V | 0-1000 |
| 101 | C相单元平均电压 | float32 | V | 0-1000 |
| 103 | 三相单元平均电压 | float32 | V | 0-1000 |
| 105 | Id电流环PI调节器积分输出 | float32 | - | -100-100 |
| 107 | Iq电流环PI调节器积分输出 | float32 | - | -100-100 |
| 109 | 主控CPU时间利用率 | float32 | % | 0-100 |
| 111 | 故障自动启动次数 | float32 | 次 | 0-1000 |
| 113 | 远程SVG输出无功 | float32 | var | -1000000-1000000 |
| 115 | 电压无功模式电压调节器输出 | float32 | - | -100-100 |
| 117 | 负序电压d轴分量 | float32 | V | -100-100 |
| 119 | 负序电压q轴分量 | float32 | V | -100-100 |
| 121 | 负序电流d轴分量 | float32 | A | -100-100 |

### 5.2 数据分组说明

**电压类参数**（地址065-075）：
- 包含三相线电压、dq轴电压分量
- 用于电网状态监测和故障诊断

**电流类参数**（地址075-087）：
- 包含SVG三相电流、负载电流
- 用于功率计算和过载保护

**功率类参数**（地址081-083）：
- 包含有功功率和无功功率
- 用于功率控制和电能质量分析

**控制类参数**（地址105-115）：
- 包含PI调节器输出、CPU利用率
- 用于系统状态监控和性能优化

## 6. Python代码示例

### 6.1 IEEE754转换函数

```python
import struct
import binascii

def bytes_to_float(byte_data):
    """
    将4字节小端序数据转换为IEEE754浮点数
    
    Args:
        byte_data: 4字节的字节数据
        
    Returns:
        float: 转换后的浮点数值
    """
    if len(byte_data) != 4:
        raise ValueError("数据长度必须为4字节")
    
    # 小端序转换
    return struct.unpack('<f', byte_data)[0]

def hex_string_to_float(hex_string):
    """
    将十六进制字符串转换为IEEE754浮点数
    
    Args:
        hex_string: 8位十六进制字符串（如"5DA5893A"）
        
    Returns:
        float: 转换后的浮点数值
    """
    # 将十六进制字符串转换为字节
    byte_data = binascii.unhexlify(hex_string)
    return bytes_to_float(byte_data)
```

### 6.2 完整数据解析器

```python
class C4DSPStatusParser:
    """
    C4 DSP状态数据解析器
    用于解析DSP状态查询命令的响应数据
    """
    
    def __init__(self):
        # 参数名称映射表
        self.parameter_names = [
            "AB线电压", "BC线电压", "CA线电压", "电压Ud", "电压Uq",
            "A相SVG电流有效值", "B相SVG电流有效值", "C相SVG电流有效值",
            "SVG输出有功", "SVG输出无功电流", "负载有功电流", "负载无功电流",
            "网侧电压幅度", "高压侧电压幅度", "上级母线电压Ud", "上级母线电压Uq",
            "A相单元平均电压", "B相单元平均电压", "C相单元平均电压", "三相单元平均电压",
            "Id电流环PI调节器积分输出", "Iq电流环PI调节器积分输出",
            "主控CPU时间利用率", "故障自动启动次数", "远程SVG输出无功",
            "电压无功模式电压调节器输出", "负序电压d轴分量", "负序电压q轴分量",
            "负序电流d轴分量", "负序电流q轴分量", "零序电压分量", "零序电流分量",
            "A相电压THD", "B相电压THD", "C相电压THD", "A相电流THD",
            "B相电流THD", "C相电流THD", "电网频率", "功率因数",
            "视在功率", "有功功率", "无功功率", "温度1",
            "温度2", "温度3", "预留参数1", "预留参数2"
        ]
    
    def parse_response(self, response_data):
        """
        解析完整的响应数据
        
        Args:
            response_data: 完整的响应帧数据（字节串）
            
        Returns:
            dict: 解析后的参数值字典
        """
        if len(response_data) < 202:
            raise ValueError("响应数据长度不足")
        
        # 提取状态数据部分（跳过帧头、命令码、长度等）
        status_data = response_data[10:202]
        
        # 解析48个浮点数
        values = {}
        for i in range(48):
            start_idx = i * 4
            end_idx = start_idx + 4
            
            if end_idx <= len(status_data):
                byte_chunk = status_data[start_idx:end_idx]
                value = bytes_to_float(byte_chunk)
                param_name = self.parameter_names[i]
                values[param_name] = value
        
        return values
    
    def parse_hex_string(self, hex_string):
        """
        从十六进制字符串解析数据
        
        Args:
            hex_string: 十六进制字符串（无空格）
            
        Returns:
            dict: 解析后的参数值字典
        """
        # 移除空格并转换为字节
        hex_string = hex_string.replace(" ", "")
        byte_data = binascii.unhexlify(hex_string)
        return self.parse_response(byte_data)
    
    def format_output(self, values):
        """
        格式化输出解析结果
        
        Args:
            values: 解析后的参数字典
            
        Returns:
            str: 格式化的输出字符串
        """
        output = []
        output.append("=" * 60)
        output.append("C4 DSP状态数据解析结果")
        output.append("=" * 60)
        
        for i, (param_name, value) in enumerate(values.items(), 1):
            unit = self.get_unit(param_name)
            output.append(f"{i:2d}. {param_name:25s}: {value:12.6f} {unit}")
        
        return "\n".join(output)
    
    def get_unit(self, param_name):
        """获取参数的单位"""
        unit_map = {
            "电压": "V", "电流": "A", "有功": "W", "无功": "var",
            "功率": "W", "利用率": "%", "次数": "次", "温度": "°C",
            "频率": "Hz", "因数": "", "THD": "%"
        }
        
        for key, unit in unit_map.items():
            if key in param_name:
                return unit
        return ""

# 使用示例
if __name__ == "__main__":
    parser = C4DSPStatusParser()
    
    # 示例数据
    example_data = "EB900101C4C00000005DA5893AF5C94A3AF902043A8EF488B9210B8A3AE193DA3A313E523B482C1E3B41E55AB9FB56B2395752CB34FAD33B3A60BDE73BCC53A8397E424E3A9CCF2CB900000000000000000000000000000000000000000CDCCCC3DE02D2C3F0000000000000000000000001BF32C368D9602384B51BB39BF4A833A0000000000000000B003FE39B81E853FFF8FCD3AFF8FCDBAEC97FFBF6F327F3F14DD883A3800483A502A033A00000000E193DA3A313E523B482C1E3BC14DD23A42104C3B43CB1C3B53AAAB"
    
    try:
        values = parser.parse_hex_string(example_data)
        print(parser.format_output(values))
    except Exception as e:
        print(f"解析错误: {e}")
```

### 6.3 数据验证函数

```python
def validate_c4_response(response_data):
    """
    验证C4响应数据的完整性
    
    Args:
        response_data: 响应数据字节串
        
    Returns:
        tuple: (is_valid, error_message)
    """
    if len(response_data) < 202:
        return False, f"数据长度不足: {len(response_data)} < 202"
    
    # 验证帧头
    if response_data[0:4] != b'\xEB\x90\x01\x01':
        return False, "帧头错误"
    
    # 验证命令码
    if response_data[4] != 0xC4:
        return False, "命令码错误"
    
    # 验证数据长度
    if response_data[5] != 0xC0:
        return False, "数据长度错误"
    
    # 验证帧尾
    if response_data[-2:] != b'\xAA\xAB':
        return False, "帧尾错误"
    
    return True, "数据有效"

# 使用示例
response = bytes.fromhex(example_data)
is_valid, message = validate_c4_response(response)
print(f"验证结果: {message}")
```

## 7. 注意事项与故障排查

### 7.1 常见错误

1. **数据长度错误**：确保响应数据完整，长度为202字节
2. **字节序错误**：必须使用小端模式解析浮点数
3. **单位混淆**：注意不同参数的单位差异
4. **数值异常**：检查是否为NaN或无穷大值

### 7.2 调试建议

1. **日志记录**：记录原始十六进制数据便于调试
2. **数据校验**：实现完整的帧格式验证
3. **异常处理**：添加数值范围检查
4. **实时监控**：建立数据变化趋势监控

### 7.3 性能优化

1. **缓存机制**：缓存解析结果减少重复计算
2. **批量处理**：支持批量数据解析
3. **异步处理**：使用异步IO提高响应速度
4. **内存管理**：及时释放大数组内存

## 8. 相关文档

- [MODBUS+自由协议点表0722](MODBUS+自由协议点表0722/)
- [MQTT统一配置使用说明](MQTT统一配置使用说明.md)
- [写入命令逻辑](写入命令逻辑.md)
- [自由协议分析](自由协议分析.md)