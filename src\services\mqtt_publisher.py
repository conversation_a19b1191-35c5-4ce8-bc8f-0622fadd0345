#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MQTT消息推送服务
用于将C0解析数据推送到MQTT服务器
参考FastBee_sdk.py进行优化
"""

import json
import time
import logging
import threading
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional

try:
    import paho.mqtt.client as mqtt
except ImportError:
    print("请安装paho-mqtt库: pip install paho-mqtt")
    exit(1)

class MQTTConfigManager:
    """
    MQTT配置管理器
    统一管理所有命令通道的MQTT配置信息
    """
    
    def __init__(self, config_file: str = "../../config/mqtt_config.json"):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        # 处理相对路径，转换为基于项目根目录的绝对路径
        if not Path(config_file).is_absolute():
            # 获取项目根目录（假设当前文件在src/services/目录下）
            project_root = Path(__file__).parent.parent.parent
            config_file = str(project_root / config_file.lstrip('../'))
        
        self.config_file = config_file
        self.config = None
        self.load_config()
    
    def load_config(self):
        """
        加载MQTT配置文件
        """
        try:
            config_path = Path(self.config_file)
            if not config_path.exists():
                raise FileNotFoundError(f"配置文件不存在: {self.config_file}")
            
            with open(config_path, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            
            print(f"MQTT配置加载成功: {self.config_file}")
            
        except Exception as e:
            print(f"加载MQTT配置失败: {e}")
            raise
    
    def get_channel_config(self, channel: str) -> Dict[str, Any]:
        """
        获取指定通道的配置信息
        
        Args:
            channel: 通道名称 (C0, 59, C5_D6_D7_D8)
            
        Returns:
            通道配置字典
        """
        if not self.config:
            raise ValueError("配置未加载")
        

        if channel not in self.config['channels']:
            raise ValueError(f"未找到通道 {channel} 的配置")
        
        channel_config = self.config['channels'][channel].copy()
        channel_config['host'] = self.config['mqtt_server']['host']
        channel_config['port'] = self.config['mqtt_server']['port']
        
        return channel_config
    
    def get_server_config(self) -> Dict[str, Any]:
        """
        获取MQTT服务器配置
        
        Returns:
            服务器配置字典
        """
        if not self.config:
            raise ValueError("配置未加载")
        
        return self.config['mqtt_server']
    
    def get_all_channels(self) -> list:
        """
        获取所有可用通道列表
        
        Returns:
            通道名称列表
        """
        if not self.config:
            raise ValueError("配置未加载")
        
        return list(self.config['channels'].keys())

class MQTT59Publisher:
    """
    MQTT59数据推送服务类
    专门处理59数据的MQTT推送和订阅
    """
    
    def __init__(self, config_file: str = "../../config/mqtt_config.json"):
        """
        初始化MQTT59推送服务
        
        Args:
            config_file: MQTT配置文件路径
        """
        # 处理相对路径，转换为基于项目根目录的绝对路径
        if not Path(config_file).is_absolute():
            project_root = Path(__file__).parent.parent.parent
            config_file = str(project_root / config_file.lstrip('../'))
        
        self.config_manager = MQTTConfigManager(config_file)
        self.mqtt_config = None
        self.client = None
        self.connected = False
        self.connection_status = -1
        self.max_retry_count = 5
        self.retry_count = 0
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        # 加载MQTT59配置
        self.load_mqtt59_config()
        
        # 初始化MQTT客户端
        self.init_mqtt_client()
    
    def load_mqtt59_config(self):
        """
        加载MQTT59配置信息
        """
        try:
            # 使用配置管理器获取59通道配置
            self.mqtt_config = self.config_manager.get_channel_config('59')
            
            self.logger.info(f"MQTT59配置加载成功: {self.mqtt_config['host']}:{self.mqtt_config['port']}")
            self.logger.info(f"客户端ID: {self.mqtt_config['clientId']}")
            self.logger.info(f"发布主题: {self.mqtt_config['reportTopic']}")
            self.logger.info(f"订阅主题: {self.mqtt_config['subscribeTopic']}")
            
        except Exception as e:
            self.logger.error(f"加载MQTT59配置失败: {e}")
            raise
    
    def init_mqtt_client(self):
        """
        初始化MQTT客户端
        """
        try:
            # 创建MQTT客户端，使用配置中的clientId
            self.client = mqtt.Client(client_id=self.mqtt_config['clientId'])
            
            # 设置用户名和密码
            self.client.username_pw_set(
                username=self.mqtt_config['username'],
                password=self.mqtt_config['passwd']
            )
            
            # 设置回调函数
            self.client.on_connect = self.on_connect
            self.client.on_disconnect = self.on_disconnect
            self.client.on_publish = self.on_publish
            self.client.on_message = self.on_message
            
            # 设置连接保持时间和重连参数
            self.client.reconnect_delay_set(min_delay=1, max_delay=120)
            
            self.logger.info("MQTT59客户端初始化成功")
            
        except Exception as e:
            self.logger.error(f"MQTT59客户端初始化失败: {e}")
            raise
    
    def on_connect(self, client, userdata, flags, rc):
        """
        MQTT连接回调函数
        """
        if rc == 0:
            self.connected = True
            self.connection_status = 0
            self.retry_count = 0
            self.logger.info("MQTT59连接成功")
            
            # 订阅控制主题
            subscribe_topic = self.mqtt_config.get('subscribeTopic')
            if subscribe_topic:
                client.subscribe(subscribe_topic, 1)
                self.logger.info(f"订阅59控制主题: {subscribe_topic}")
                
        else:
            self.connected = False
            self.connection_status = rc
            self.retry_count += 1
            error_messages = {
                1: "连接失败-不正确的协议版本",
                2: "连接失败-无效的客户端标识符", 
                3: "连接失败-服务器不可用",
                4: "连接失败-错误的用户名或密码",
                5: "连接失败-未授权"
            }
            error_msg = error_messages.get(rc, f"连接失败-未知错误({rc})")
            self.logger.error(error_msg)
            
            if self.retry_count < self.max_retry_count:
                self.logger.info(f"3秒后进行第{self.retry_count}次重连...")
                threading.Timer(3.0, self.reconnect).start()
    
    def on_disconnect(self, client, userdata, rc):
        """
        MQTT断开连接回调函数
        """
        self.connected = False
        self.connection_status = -1
        if rc != 0:
            self.logger.warning(f"MQTT59意外断开连接，代码: {rc}")
            if self.retry_count < self.max_retry_count:
                self.logger.info("5秒后尝试重连...")
                threading.Timer(5.0, self.reconnect).start()
        else:
            self.logger.info("MQTT59正常断开连接")
    
    def on_message(self, client, userdata, msg):
        """
        MQTT消息接收回调函数
        处理订阅的控制消息，更新59数据的writeValue字段
        """
        try:
            topic = msg.topic
            payload = msg.payload.decode('utf-8')
            self.logger.info(f"收到59控制消息 - 主题: {topic}, 内容: {payload}")
            
            # 解析JSON消息
            try:
                message_data = json.loads(payload)
                if isinstance(message_data, list):
                    # 处理消息列表
                    for item in message_data:
                        if isinstance(item, dict) and 'id' in item and 'value' in item:
                            self.update_59_write_value(item['id'], item['value'])
                elif isinstance(message_data, dict) and 'id' in message_data and 'value' in message_data:
                    # 处理单个消息
                    self.update_59_write_value(message_data['id'], message_data['value'])
                else:
                    self.logger.warning(f"未识别的消息格式: {payload}")
            except json.JSONDecodeError as e:
                self.logger.error(f"解析JSON消息失败: {e}")
            
        except Exception as e:
            self.logger.error(f"处理59控制消息时出错: {e}")
    
    def update_59_write_value(self, data_id: str, new_value: str):
        """
        更新59数据文件中指定ID的writeValue字段
        
        Args:
            data_id: 数据点ID
            new_value: 新的写入值
        """
        try:
            data_file = "../../data/59_parsed_data.json"
            data_path = Path(data_file)
            
            if not data_path.exists():
                self.logger.error(f"59数据文件不存在: {data_file}")
                return
            
            # 读取现有数据
            with open(data_path, 'r', encoding='utf-8') as f:
                data_list = json.load(f)
            
            # 查找并更新对应ID的writeValue
            updated = False
            for item in data_list:
                if item.get('id') == data_id:
                    old_value = item.get('writeValue', '')
                    item['writeValue'] = str(new_value)
                    item['ts'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
                    updated = True
                    self.logger.info(f"更新59数据 {data_id}: {old_value} -> {new_value}")
                    break
            
            if updated:
                # 写回文件
                with open(data_path, 'w', encoding='utf-8') as f:
                    json.dump(data_list, f, ensure_ascii=False, indent=4)
                self.logger.info(f"59数据文件已更新: {data_file}")
            else:
                self.logger.warning(f"未找到ID为 {data_id} 的59数据点")
                
        except Exception as e:
            self.logger.error(f"更新59数据writeValue时出错: {e}")
    
    def reconnect(self):
        """
        重连MQTT服务器
        """
        try:
            if not self.connected and self.retry_count < self.max_retry_count:
                self.retry_count += 1
                self.logger.info(f"尝试第{self.retry_count}次重连MQTT59服务器...")
                self.connect()
        except Exception as e:
            self.logger.error(f"重连时出错: {e}")
    
    def on_publish(self, client, userdata, mid):
        """
        MQTT消息发布回调函数
        """
        self.logger.debug(f"59消息发布成功，消息ID: {mid}")
    
    def connect(self) -> bool:
        """
        连接到MQTT服务器
        """
        try:
            host = self.mqtt_config['host']
            port = int(self.mqtt_config['port'])
            
            self.logger.info(f"正在连接MQTT59服务器: {host}:{port}")
            self.client.connect(host, port, 60)
            
            # 启动网络循环
            self.client.loop_start()
            
            # 等待连接建立
            timeout = 10
            start_time = time.time()
            while not self.connected and (time.time() - start_time) < timeout:
                time.sleep(0.1)
            
            if self.connected:
                self.logger.info("MQTT59连接建立成功")
                return True
            else:
                self.logger.error("MQTT59连接超时")
                return False
                
        except Exception as e:
            self.logger.error(f"MQTT59连接失败: {e}")
            return False
    
    def disconnect(self):
        """
        断开MQTT连接
        """
        try:
            if self.client and self.connected:
                self.client.disconnect()
                self.client.loop_stop()
                self.logger.info("MQTT59连接已断开")
            
            self.connected = False
            self.connection_status = -1
            
        except Exception as e:
            self.logger.error(f"断开MQTT59连接时出错: {e}")
    
    def load_59_data(self, data_file: str = "data/59_parsed_data.json") -> Optional[list]:
        """
        加载59解析数据
        
        Args:
            data_file: 59数据文件路径
            
        Returns:
            59解析数据列表
        """
        try:
            data_path = Path(data_file)
            if not data_path.exists():
                self.logger.warning(f"59数据文件不存在: {data_file}")
                return None
            
            with open(data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.logger.info(f"成功加载59数据，共{len(data)}个数据点")
            return data
            
        except Exception as e:
            self.logger.error(f"加载59数据失败: {e}")
            return None
    
    def create_59_mqtt_message(self, data_59: list) -> list:
        """
        创建59 MQTT消息体
        
        Args:
            data_59: 59解析数据
            
        Returns:
            59 MQTT消息体
        """
        return data_59
    
    def publish_59_data(self, data_file: str = "data/59_parsed_data.json") -> bool:
        """
        发布59数据到MQTT
        
        Args:
            data_file: 59数据文件路径
            
        Returns:
            发布是否成功
        """
        if not self.connected:
            self.logger.error("MQTT59未连接，无法发布消息")
            return False
        
        # 加载59数据
        data_59 = self.load_59_data(data_file)
        if not data_59:
            return False
        
        # 创建59消息体
        message = self.create_59_mqtt_message(data_59)
        
        try:
            # 发布59数据到指定主题
            topic = self.mqtt_config.get('reportTopic', '/197/D19EOEN59V1MJ/property/post')
            payload = json.dumps(message, ensure_ascii=False, indent=2)
            
            result = self.client.publish(topic, payload, qos=1)
            
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                self.logger.info(f"59数据发布成功到主题: {topic}")
                self.logger.debug(f"59消息内容: {payload[:200]}...")
                return True
            else:
                self.logger.error(f"59数据发布失败，错误代码: {result.rc}")
                return False
                
        except Exception as e:
            self.logger.error(f"发布59数据时发生错误: {e}")
            return False
    
    def get_connection_status(self):
        """
        获取连接状态
        """
        return {
            'connected': self.connected,
            'connection_status': self.connection_status,
            'retry_count': self.retry_count,
            'max_retry_count': self.max_retry_count,
            'client_id': self.mqtt_config.get('clientId', '') if self.mqtt_config else '',
            'server': f"{self.mqtt_config.get('host', '')}:{self.mqtt_config.get('port', '')}" if self.mqtt_config else ''
        }


class MQTTPublisher:
    """
    MQTT消息推送服务类
    参考FastBee_sdk.py优化的版本
    """
    
    def __init__(self, channel: str = 'C0', config_file: str = "../../config/mqtt_config.json"):
        """
        初始化MQTT推送服务
        
        Args:
            channel: 通道名称 ('C0', 'C5_D6_D7_D8')
            config_file: MQTT配置文件路径
        """
        # 处理相对路径，转换为基于项目根目录的绝对路径
        if not Path(config_file).is_absolute():
            project_root = Path(__file__).parent.parent.parent
            config_file = str(project_root / config_file.lstrip('../'))
        
        self.channel = channel
        self.config_manager = MQTTConfigManager(config_file)
        self.mqtt_config = None
        self.client = None
        self.connected = False
        self.connection_status = -1  # 连接状态标志位，参考FastBee_sdk.py
        self.publish_timer = None  # 定时发布定时器
        self.monitor_interval = 30  # 监控间隔（秒）
        self.max_retry_count = 5  # 最大重试次数
        self.retry_count = 0  # 当前重试次数
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(f"{__name__}_{channel}")
        
        # 加载MQTT配置
        self.load_mqtt_config()
        
        # 初始化MQTT客户端
        self.init_mqtt_client()
    
    def load_mqtt_config(self):
        """
        加载MQTT配置信息
        """
        try:
            # 使用配置管理器获取指定通道配置
            self.mqtt_config = self.config_manager.get_channel_config(self.channel)
            
            self.logger.info(f"MQTT配置加载成功 [{self.channel}]: {self.mqtt_config['host']}:{self.mqtt_config['port']}")
            self.logger.info(f"客户端ID [{self.channel}]: {self.mqtt_config['clientId']}")
            self.logger.info(f"发布主题 [{self.channel}]: {self.mqtt_config['reportTopic']}")
            self.logger.info(f"订阅主题 [{self.channel}]: {self.mqtt_config['subscribeTopic']}")
            
        except Exception as e:
            self.logger.error(f"加载MQTT配置失败 [{self.channel}]: {e}")
            raise
    
    def init_mqtt_client(self):
        """
        初始化MQTT客户端
        参考FastBee_sdk.py的客户端配置
        """
        try:
            # 创建MQTT客户端，使用配置中的clientId
            self.client = mqtt.Client(client_id=self.mqtt_config['clientId'])
            
            # 设置用户名和密码
            self.client.username_pw_set(
                username=self.mqtt_config['username'],
                password=self.mqtt_config['passwd']
            )
            
            # 设置回调函数
            self.client.on_connect = self.on_connect
            self.client.on_disconnect = self.on_disconnect
            self.client.on_publish = self.on_publish
            self.client.on_message = self.on_message  # 添加消息接收回调
            
            # 设置连接保持时间和重连参数
            self.client.reconnect_delay_set(min_delay=1, max_delay=120)
            
            self.logger.info("MQTT客户端初始化成功")
            
        except Exception as e:
            self.logger.error(f"MQTT客户端初始化失败: {e}")
            raise
    
    def on_connect(self, client, userdata, flags, rc):
        """
        MQTT连接回调函数
        参考FastBee_sdk.py的连接处理逻辑
        
        Args:
            client: MQTT客户端实例
            userdata: 用户数据
            flags: 连接标志
            rc: 连接结果代码
        """
        if rc == 0:
            self.connected = True
            self.connection_status = 0
            self.retry_count = 0  # 重置重试计数
            self.logger.info("MQTT连接成功")
            
            # 订阅控制主题（如果需要接收控制指令）
            subscribe_topic = self.mqtt_config.get('subscribeTopic')
            if subscribe_topic:
                client.subscribe(subscribe_topic, 1)
                self.logger.info(f"订阅主题: {subscribe_topic}")
                
        else:
            self.connected = False
            self.connection_status = rc
            self.retry_count += 1
            error_messages = {
                1: "连接失败-不正确的协议版本",
                2: "连接失败-无效的客户端标识符", 
                3: "连接失败-服务器不可用",
                4: "连接失败-错误的用户名或密码",
                5: "连接失败-未授权"
            }
            error_msg = error_messages.get(rc, f"连接失败-未知错误({rc})")
            self.logger.error(error_msg)
            
            # 如果重试次数未超过最大值，则尝试重连
            if self.retry_count < self.max_retry_count:
                self.logger.info(f"3秒后进行第{self.retry_count}次重连...")
                threading.Timer(3.0, self.reconnect).start()
            else:
                self.logger.error(f"重连失败次数已达到最大值({self.max_retry_count})，停止重连")
    
    def on_disconnect(self, client, userdata, rc):
        """
        MQTT断开连接回调函数
        
        Args:
            client: MQTT客户端实例
            userdata: 用户数据
            rc: 断开连接代码
        """
        self.connected = False
        self.connection_status = -1
        if rc != 0:
            self.logger.warning(f"MQTT意外断开连接，代码: {rc}")
            # 意外断开时尝试重连
            if self.retry_count < self.max_retry_count:
                self.logger.info("5秒后尝试重连...")
                threading.Timer(5.0, self.reconnect).start()
        else:
            self.logger.info("MQTT正常断开连接")
    
    def on_message(self, client, userdata, msg):
        """
        MQTT消息接收回调函数
        处理订阅的控制消息，更新对应数据的writeValue字段

        Args:
            client: MQTT客户端实例
            userdata: 用户数据
            msg: 接收到的消息
        """
        try:
            topic = msg.topic
            payload = msg.payload.decode('utf-8')
            self.logger.info(f"收到控制消息 - 主题: {topic}, 内容: {payload}")

            # 解析JSON消息
            try:
                message_data = json.loads(payload)
                if isinstance(message_data, list):
                    # 处理消息列表
                    for item in message_data:
                        if isinstance(item, dict) and 'id' in item and 'value' in item:
                            self.update_channel_write_value(item['id'], item['value'])
                elif isinstance(message_data, dict) and 'id' in message_data and 'value' in message_data:
                    # 处理单个消息
                    self.update_channel_write_value(message_data['id'], message_data['value'])
                else:
                    self.logger.warning(f"未识别的消息格式: {payload}")
            except json.JSONDecodeError as e:
                self.logger.error(f"解析JSON消息失败: {e}")

        except Exception as e:
            self.logger.error(f"处理控制消息时出错: {e}")

    def update_channel_write_value(self, data_id: str, new_value: str):
        """
        根据通道类型更新对应数据文件中指定ID的writeValue字段

        Args:
            data_id: 数据点ID
            new_value: 新的写入值
        """
        # 根据通道类型确定要更新的数据文件
        if self.channel == 'C0':
            self.update_c0_write_value(data_id, new_value)
        elif self.channel == 'C5':
            self.update_c5_write_value(data_id, new_value)
        elif self.channel == 'C5_D6_D7_D8':
            # 对于合并通道，需要判断数据ID属于哪个具体类型
            self.update_c5_d6_d7_d8_write_value(data_id, new_value)
        elif self.channel == 'D0':
            self.update_d0_write_value(data_id, new_value)
        elif self.channel == 'D1':
            self.update_d1_write_value(data_id, new_value)
        elif self.channel == 'D2':
            self.update_d2_write_value(data_id, new_value)
        elif self.channel == 'D3':
            self.update_d3_write_value(data_id, new_value)
        elif self.channel == 'D4':
            self.update_d4_write_value(data_id, new_value)
        elif self.channel == 'D5':
            self.update_d5_write_value(data_id, new_value)
        elif self.channel == 'D6':
            self.update_d6_write_value(data_id, new_value)
        elif self.channel == 'D7':
            self.update_d7_write_value(data_id, new_value)
        elif self.channel == 'D8':
            self.update_d8_write_value(data_id, new_value)
        else:
            self.logger.warning(f"未知通道类型: {self.channel}")

    def update_c0_write_value(self, data_id: str, new_value: str):
        """
        更新C0数据文件中指定ID的writeValue字段

        Args:
            data_id: 数据点ID
            new_value: 新的写入值
        """
        self._update_data_file_write_value("../../data/c0_parsed_data.json", "C0", data_id, new_value)

    def update_c5_write_value(self, data_id: str, new_value: str):
        """
        更新C5数据文件中指定ID的writeValue字段

        Args:
            data_id: 数据点ID
            new_value: 新的写入值
        """
        self._update_data_file_write_value("../../data/c5_parsed_data.json", "C5", data_id, new_value)

    def update_c5_d6_d7_d8_write_value(self, data_id: str, new_value: str):
        """
        更新C5_D6_D7_D8合并通道数据文件中指定ID的writeValue字段

        Args:
            data_id: 数据点ID
            new_value: 新的写入值
        """
        # 尝试在各个数据文件中查找并更新（包括D0-D5）
        data_files = [
            ("../../data/c5_parsed_data.json", "C5"),
            ("../../data/d0_parsed_data.json", "D0"),
            ("../../data/d1_parsed_data.json", "D1"),
            ("../../data/d2_parsed_data.json", "D2"),
            ("../../data/d3_parsed_data.json", "D3"),
            ("../../data/d4_parsed_data.json", "D4"),
            ("../../data/d5_parsed_data.json", "D5"),
            ("../../data/d6_parsed_data.json", "D6"),
            ("../../data/d7_parsed_data.json", "D7"),
            ("../../data/d8_parsed_data.json", "D8")
        ]

        for data_file, data_type in data_files:
            if self._update_data_file_write_value(data_file, data_type, data_id, new_value):
                break  # 找到并更新成功后退出循环

    def _update_data_file_write_value(self, data_file: str, data_type: str, data_id: str, new_value: str) -> bool:
        """
        更新指定数据文件中指定ID的writeValue字段

        Args:
            data_file: 数据文件路径
            data_type: 数据类型（用于日志）
            data_id: 数据点ID
            new_value: 新的写入值

        Returns:
            是否更新成功
        """
        try:
            data_path = Path(data_file)

            if not data_path.exists():
                self.logger.warning(f"{data_type}数据文件不存在: {data_file}")
                return False

            # 读取现有数据
            with open(data_path, 'r', encoding='utf-8') as f:
                data_list = json.load(f)

            # 查找并更新对应ID的writeValue
            updated = False
            for item in data_list:
                if item.get('id') == data_id:
                    old_value = item.get('writeValue', '')
                    item['writeValue'] = str(new_value)
                    item['ts'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
                    updated = True
                    self.logger.info(f"更新{data_type}数据 {data_id}: {old_value} -> {new_value}")
                    break

            if updated:
                # 写回文件
                with open(data_path, 'w', encoding='utf-8') as f:
                    json.dump(data_list, f, ensure_ascii=False, indent=4)
                self.logger.info(f"{data_type}数据文件已更新: {data_file}")
                return True
            else:
                self.logger.debug(f"未在{data_type}数据中找到ID为 {data_id} 的数据点")
                return False

        except Exception as e:
            self.logger.error(f"更新{data_type}数据writeValue时出错: {e}")
            return False

    def update_d0_write_value(self, data_id: str, new_value: str):
        """
        更新D0数据文件中指定ID的writeValue字段

        Args:
            data_id: 数据点ID
            new_value: 新的写入值
        """
        self._update_data_file_write_value("../../data/d0_parsed_data.json", "D0", data_id, new_value)

    def update_d1_write_value(self, data_id: str, new_value: str):
        """
        更新D1数据文件中指定ID的writeValue字段

        Args:
            data_id: 数据点ID
            new_value: 新的写入值
        """
        self._update_data_file_write_value("../../data/d1_parsed_data.json", "D1", data_id, new_value)

    def update_d2_write_value(self, data_id: str, new_value: str):
        """
        更新D2数据文件中指定ID的writeValue字段

        Args:
            data_id: 数据点ID
            new_value: 新的写入值
        """
        self._update_data_file_write_value("../../data/d2_parsed_data.json", "D2", data_id, new_value)

    def update_d3_write_value(self, data_id: str, new_value: str):
        """
        更新D3数据文件中指定ID的writeValue字段

        Args:
            data_id: 数据点ID
            new_value: 新的写入值
        """
        self._update_data_file_write_value("../../data/d3_parsed_data.json", "D3", data_id, new_value)

    def update_d4_write_value(self, data_id: str, new_value: str):
        """
        更新D4数据文件中指定ID的writeValue字段

        Args:
            data_id: 数据点ID
            new_value: 新的写入值
        """
        self._update_data_file_write_value("../../data/d4_parsed_data.json", "D4", data_id, new_value)

    def update_d5_write_value(self, data_id: str, new_value: str):
        """
        更新D5数据文件中指定ID的writeValue字段

        Args:
            data_id: 数据点ID
            new_value: 新的写入值
        """
        self._update_data_file_write_value("../../data/d5_parsed_data.json", "D5", data_id, new_value)

    def update_d6_write_value(self, data_id: str, new_value: str):
        """
        更新D6数据文件中指定ID的writeValue字段

        Args:
            data_id: 数据点ID
            new_value: 新的写入值
        """
        self._update_data_file_write_value("../../data/d6_parsed_data.json", "D6", data_id, new_value)

    def update_d7_write_value(self, data_id: str, new_value: str):
        """
        更新D7数据文件中指定ID的writeValue字段

        Args:
            data_id: 数据点ID
            new_value: 新的写入值
        """
        self._update_data_file_write_value("../../data/d7_parsed_data.json", "D7", data_id, new_value)

    def update_d8_write_value(self, data_id: str, new_value: str):
        """
        更新D8数据文件中指定ID的writeValue字段

        Args:
            data_id: 数据点ID
            new_value: 新的写入值
        """
        self._update_data_file_write_value("../../data/d8_parsed_data.json", "D8", data_id, new_value)

    def reconnect(self):
        """
        重连MQTT服务器
        参考FastBee_sdk.py的重连机制
        """
        try:
            if not self.connected and self.retry_count < self.max_retry_count:
                self.retry_count += 1
                self.logger.info(f"尝试第{self.retry_count}次重连MQTT服务器...")
                self.connect()
        except Exception as e:
            self.logger.error(f"重连时出错: {e}")
    
    def on_publish(self, client, userdata, mid):
        """
        MQTT消息发布回调函数
        
        Args:
            client: MQTT客户端实例
            userdata: 用户数据
            mid: 消息ID
        """
        self.logger.debug(f"消息发布成功，消息ID: {mid}")
    
    def connect(self) -> bool:
        """
        连接到MQTT服务器
        
        Returns:
            连接是否成功
        """
        try:
            host = self.mqtt_config['host']
            port = int(self.mqtt_config['port'])
            
            self.logger.info(f"正在连接MQTT服务器: {host}:{port}")
            self.client.connect(host, port, 60)
            
            # 启动网络循环
            self.client.loop_start()
            
            # 等待连接建立
            timeout = 10
            start_time = time.time()
            while not self.connected and (time.time() - start_time) < timeout:
                time.sleep(0.1)
            
            if self.connected:
                self.logger.info("MQTT连接建立成功")
                return True
            else:
                self.logger.error("MQTT连接超时")
                return False
                
        except Exception as e:
            self.logger.error(f"MQTT连接失败: {e}")
            return False
    
    def disconnect(self):
        """
        断开MQTT连接
        参考FastBee_sdk.py的断开连接处理
        """
        try:
            # 停止定期发布
            self.stop_periodic_publish()
            
            if self.client and self.connected:
                self.client.disconnect()
                self.client.loop_stop()
                self.logger.info("MQTT连接已断开")
            
            self.connected = False
            self.connection_status = -1
            
        except Exception as e:
            self.logger.error(f"断开MQTT连接时出错: {e}")
    
    def get_connection_status(self):
        """
        获取连接状态
        参考FastBee_sdk.py的状态查询
        
        Returns:
            dict: 连接状态信息
        """
        return {
            'connected': self.connected,
            'connection_status': self.connection_status,
            'retry_count': self.retry_count,
            'max_retry_count': self.max_retry_count,
            'client_id': self.mqtt_config.get('clientId', '') if self.mqtt_config else '',
            'server': f"{self.mqtt_config.get('host', '')}:{self.mqtt_config.get('port', '')}" if self.mqtt_config else ''
        }
    
    def health_check(self):
        """
        健康检查
        参考FastBee_sdk.py的健康检查机制
        
        Returns:
            bool: 健康状态
        """
        try:
            if not self.connected:
                self.logger.warning("健康检查失败: MQTT未连接")
                return False
            
            # 检查客户端状态
            if not self.client or not self.client.is_connected():
                self.logger.warning("健康检查失败: 客户端状态异常")
                return False
            
            self.logger.debug("健康检查通过")
            return True
            
        except Exception as e:
            self.logger.error(f"健康检查异常: {e}")
            return False
    
    def load_d6_data(self, data_file: str = "data/d6_parsed_data.json") -> Optional[list]:
        """
        加载D6解析数据
        
        Args:
            data_file: D6数据文件路径
            
        Returns:
            D6解析数据列表
        """
        try:
            data_path = Path(data_file)
            if not data_path.exists():
                self.logger.warning(f"D6数据文件不存在: {data_file}")
                return None
            
            with open(data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.logger.info(f"成功加载D6数据，共{len(data)}个数据点")
            return data
            
        except Exception as e:
            self.logger.error(f"加载D6数据失败: {e}")
            return None

    def load_d7_data(self, data_file: str = "data/d7_parsed_data.json") -> Optional[list]:
        """
        加载D7解析数据
        
        Args:
            data_file: D7数据文件路径
            
        Returns:
            D7解析数据列表
        """
        try:
            data_path = Path(data_file)
            if not data_path.exists():
                self.logger.warning(f"D7数据文件不存在: {data_file}")
                return None
            
            with open(data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.logger.info(f"成功加载D7数据，共{len(data)}个数据点")
            return data
            
        except Exception as e:
            self.logger.error(f"加载D7数据失败: {e}")
            return None

    def load_d8_data(self, data_file: str = "data/d8_parsed_data.json") -> Optional[list]:
        """
        加载D8解析数据
        
        Args:
            data_file: D8数据文件路径
            
        Returns:
            D8解析数据列表
        """
        try:
            data_path = Path(data_file)
            if not data_path.exists():
                self.logger.warning(f"D8数据文件不存在: {data_file}")
                return None
            
            with open(data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.logger.info(f"成功加载D8数据，共{len(data)}个数据点")
            return data
            
        except Exception as e:
            self.logger.error(f"加载D8数据失败: {e}")
            return None

    def load_d0_data(self, data_file: str = "data/d0_parsed_data.json") -> Optional[list]:
        """
        加载D0解析数据
        
        Args:
            data_file: D0数据文件路径
            
        Returns:
            D0解析数据列表
        """
        try:
            data_path = Path(data_file)
            if not data_path.exists():
                self.logger.warning(f"D0数据文件不存在: {data_file}")
                return None
            
            with open(data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.logger.info(f"成功加载D0数据，共{len(data)}个数据点")
            return data
            
        except Exception as e:
            self.logger.error(f"加载D0数据失败: {e}")
            return None

    def load_d1_data(self, data_file: str = "data/d1_parsed_data.json") -> Optional[list]:
        """
        加载D1解析数据
        
        Args:
            data_file: D1数据文件路径
            
        Returns:
            D1解析数据列表
        """
        try:
            data_path = Path(data_file)
            if not data_path.exists():
                self.logger.warning(f"D1数据文件不存在: {data_file}")
                return None
            
            with open(data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.logger.info(f"成功加载D1数据，共{len(data)}个数据点")
            return data
            
        except Exception as e:
            self.logger.error(f"加载D1数据失败: {e}")
            return None

    def load_d2_data(self, data_file: str = "data/d2_parsed_data.json") -> Optional[list]:
        """
        加载D2解析数据
        
        Args:
            data_file: D2数据文件路径
            
        Returns:
            D2解析数据列表
        """
        try:
            data_path = Path(data_file)
            if not data_path.exists():
                self.logger.warning(f"D2数据文件不存在: {data_file}")
                return None
            
            with open(data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.logger.info(f"成功加载D2数据，共{len(data)}个数据点")
            return data
            
        except Exception as e:
            self.logger.error(f"加载D2数据失败: {e}")
            return None
            
    def load_d3_data(self, data_file: str = "data/d3_parsed_data.json") -> Optional[list]:
        """
        加载D3解析数据
        
        Args:
            data_file: D3数据文件路径
            
        Returns:
            D3解析数据列表
        """
        try:
            data_path = Path(data_file)
            if not data_path.exists():
                self.logger.warning(f"D3数据文件不存在: {data_file}")
                return None
            
            with open(data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.logger.info(f"成功加载D3数据，共{len(data)}个数据点")
            return data
            
        except Exception as e:
            self.logger.error(f"加载D3数据失败: {e}")
            return None
            
    def load_d4_data(self, data_file: str = "data/d4_parsed_data.json") -> Optional[list]:
        """
        加载D4解析数据
        
        Args:
            data_file: D4数据文件路径
            
        Returns:
            D4解析数据列表
        """
        try:
            data_path = Path(data_file)
            if not data_path.exists():
                self.logger.warning(f"D4数据文件不存在: {data_file}")
                return None
            
            with open(data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.logger.info(f"成功加载D4数据，共{len(data)}个数据点")
            return data
            
        except Exception as e:
            self.logger.error(f"加载D4数据失败: {e}")
            return None
            
    def load_d5_data(self, data_file: str = "data/d5_parsed_data.json") -> Optional[list]:
        """
        加载D5解析数据
        
        Args:
            data_file: D5数据文件路径
            
        Returns:
            D5解析数据列表
        """
        try:
            data_path = Path(data_file)
            if not data_path.exists():
                self.logger.warning(f"D5数据文件不存在: {data_file}")
                return None
            
            with open(data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.logger.info(f"成功加载D5数据，共{len(data)}个数据点")
            return data
            
        except Exception as e:
            self.logger.error(f"加载D5数据失败: {e}")
            return None

    def create_d6_mqtt_message(self, d6_data: list) -> list:
        """
        创建D6 MQTT消息体
        
        Args:
            d6_data: D6解析数据
            
        Returns:
            D6 MQTT消息体
        """
        return d6_data

    def create_d7_mqtt_message(self, d7_data: list) -> list:
        """
        创建D7 MQTT消息体
        
        Args:
            d7_data: D7解析数据
            
        Returns:
            D7 MQTT消息体
        """
        return d7_data

    def create_d8_mqtt_message(self, d8_data: list) -> list:
        """
        创建D8 MQTT消息体
        
        Args:
            d8_data: D8解析数据
            
        Returns:
            D8 MQTT消息体
        """
        return d8_data

    def create_d0_mqtt_message(self, d0_data: list) -> list:
        """
        创建D0 MQTT消息体
        
        Args:
            d0_data: D0解析数据
            
        Returns:
            D0 MQTT消息体
        """
        return d0_data

    def create_d1_mqtt_message(self, d1_data: list) -> list:
        """
        创建D1 MQTT消息体
        
        Args:
            d1_data: D1解析数据
            
        Returns:
            D1 MQTT消息体
        """
        return d1_data

    def create_d2_mqtt_message(self, d2_data: list) -> list:
        """
        创建D2 MQTT消息体
        
        Args:
            d2_data: D2解析数据
            
        Returns:
            D2 MQTT消息体
        """
        return d2_data
        
    def create_d3_mqtt_message(self, d3_data: list) -> list:
        """
        创建D3 MQTT消息体
        
        Args:
            d3_data: D3解析数据
            
        Returns:
            D3 MQTT消息体
        """
        return d3_data
        
    def create_d4_mqtt_message(self, d4_data: list) -> list:
        """
        创建D4 MQTT消息体
        
        Args:
            d4_data: D4解析数据
            
        Returns:
            D4 MQTT消息体
        """
        return d4_data
        
    def create_d5_mqtt_message(self, d5_data: list) -> list:
        """
        创建D5 MQTT消息体
        
        Args:
            d5_data: D5解析数据
            
        Returns:
            D5 MQTT消息体
        """
        return d5_data

    def publish_d6_data(self, data_file: str = "data/d6_parsed_data.json") -> bool:
        """
        发布D6数据到MQTT
        
        Args:
            data_file: D6数据文件路径
            
        Returns:
            发布是否成功
        """
        if not self.connected:
            self.logger.error("MQTT未连接，无法发布D6消息")
            return False
        
        # 加载D6数据
        d6_data = self.load_d6_data(data_file)
        if not d6_data:
            return False
        
        # 创建D6消息体
        message = self.create_d6_mqtt_message(d6_data)
        
        try:
            # 发布D6数据到指定主题
            topic = "/196/D19822J4J92OT/property/post"
            payload = json.dumps(message, ensure_ascii=False, indent=2)
            
            result = self.client.publish(topic, payload, qos=1)
            
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                self.logger.info(f"D6数据发布成功到主题: {topic}")
                self.logger.debug(f"D6消息内容: {payload[:200]}...")
                return True
            else:
                self.logger.error(f"D6数据发布失败，错误代码: {result.rc}")
                return False
                
        except Exception as e:
            self.logger.error(f"发布D6数据时发生错误: {e}")
            return False

    def publish_d7_data(self, data_file: str = "data/d7_parsed_data.json") -> bool:
        """
        发布D7数据到MQTT
        
        Args:
            data_file: D7数据文件路径
            
        Returns:
            发布是否成功
        """
        if not self.connected:
            self.logger.error("MQTT未连接，无法发布D7消息")
            return False
        
        # 加载D7数据
        d7_data = self.load_d7_data(data_file)
        if not d7_data:
            return False
        
        # 创建D7消息体
        message = self.create_d7_mqtt_message(d7_data)
        
        try:
            # 发布D7数据到指定主题
            topic = "/196/D19822J4J92OT/property/post"
            payload = json.dumps(message, ensure_ascii=False, indent=2)
            
            result = self.client.publish(topic, payload, qos=1)
            
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                self.logger.info(f"D7数据发布成功到主题: {topic}")
                self.logger.debug(f"D7消息内容: {payload[:200]}...")
                return True
            else:
                self.logger.error(f"D7数据发布失败，错误代码: {result.rc}")
                return False
                
        except Exception as e:
            self.logger.error(f"发布D7数据时发生错误: {e}")
            return False

    def publish_d8_data(self, data_file: str = "data/d8_parsed_data.json") -> bool:
        """
        发布D8数据到MQTT
        
        Args:
            data_file: D8数据文件路径
            
        Returns:
            发布是否成功
        """
        if not self.connected:
            self.logger.error("MQTT未连接，无法发布D8消息")
            return False
        
        # 加载D8数据
        d8_data = self.load_d8_data(data_file)
        if not d8_data:
            return False
        
        # 创建D8消息体
        message = self.create_d8_mqtt_message(d8_data)
        
        try:
            # 发布D8数据到指定主题
            topic = "/196/D19822J4J92OT/property/post"
            payload = json.dumps(message, ensure_ascii=False, indent=2)
            
            result = self.client.publish(topic, payload, qos=1)
            
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                self.logger.info(f"D8数据发布成功到主题: {topic}")
                self.logger.debug(f"D8消息内容: {payload[:200]}...")
                return True
            else:
                self.logger.error(f"D8数据发布失败，错误代码: {result.rc}")
                return False
                
        except Exception as e:
            self.logger.error(f"发布D8数据时发生错误: {e}")
            return False

    def publish_d0_data(self, data_file: str = "data/d0_parsed_data.json") -> bool:
        """
        发布D0数据到MQTT
        
        Args:
            data_file: D0数据文件路径
            
        Returns:
            发布是否成功
        """
        if not self.connected:
            self.logger.error("MQTT未连接，无法发布D0消息")
            return False
        
        # 加载D0数据
        d0_data = self.load_d0_data(data_file)
        if not d0_data:
            return False
        
        # 创建D0消息体
        message = self.create_d0_mqtt_message(d0_data)
        
        try:
            # 从配置中获取D0发布主题，如果没有则使用默认主题
            try:
                d0_config = self.config_manager.get_channel_config('D0')
                topic = d0_config['reportTopic']
            except ValueError:
                # 如果没有单独的D0配置，使用默认主题
                topic = "/195/D19DQ66713XIJ/property/post"

            payload = json.dumps(message, ensure_ascii=False, indent=2)

            result = self.client.publish(topic, payload, qos=1)

            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                self.logger.info(f"D0数据发布成功到主题: {topic}")
                self.logger.debug(f"D0消息内容: {payload[:200]}...")
                return True
            else:
                self.logger.error(f"D0数据发布失败，错误代码: {result.rc}")
                return False

        except Exception as e:
            self.logger.error(f"发布D0数据时发生错误: {e}")
            return False

    def publish_d1_data(self, data_file: str = "data/d1_parsed_data.json") -> bool:
        """
        发布D1数据到MQTT
        
        Args:
            data_file: D1数据文件路径
            
        Returns:
            发布是否成功
        """
        if not self.connected:
            self.logger.error("MQTT未连接，无法发布D1消息")
            return False
        
        # 加载D1数据
        d1_data = self.load_d1_data(data_file)
        if not d1_data:
            return False
        
        # 创建D1消息体
        message = self.create_d1_mqtt_message(d1_data)
        
        try:
            # 从配置中获取D1发布主题，如果没有则使用默认主题
            try:
                d1_config = self.config_manager.get_channel_config('D1')
                topic = d1_config['reportTopic']
            except ValueError:
                # 如果没有单独的D1配置，使用默认主题
                topic = "/195/D19DQ66713XIJ/property/post"

            payload = json.dumps(message, ensure_ascii=False, indent=2)

            result = self.client.publish(topic, payload, qos=1)

            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                self.logger.info(f"D1数据发布成功到主题: {topic}")
                self.logger.debug(f"D1消息内容: {payload[:200]}...")
                return True
            else:
                self.logger.error(f"D1数据发布失败，错误代码: {result.rc}")
                return False

        except Exception as e:
            self.logger.error(f"发布D1数据时发生错误: {e}")
            return False

    def publish_d2_data(self, data_file: str = "data/d2_parsed_data.json") -> bool:
        """
        发布D2数据到MQTT
        
        Args:
            data_file: D2数据文件路径
            
        Returns:
            发布是否成功
        """
        if not self.connected:
            self.logger.error("MQTT未连接，无法发布D2消息")
            return False
        
        # 加载D2数据
        d2_data = self.load_d2_data(data_file)
        if not d2_data:
            return False
        
        # 创建D2消息体
        message = self.create_d2_mqtt_message(d2_data)
        
        try:
            # 发布D2数据到指定主题
            topic = "/195/D19DQ66713XIJ/property/post"
            payload = json.dumps(message, ensure_ascii=False, indent=2)
            
            result = self.client.publish(topic, payload, qos=1)
            
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                self.logger.info(f"D2数据发布成功到主题: {topic}")
                self.logger.debug(f"D2消息内容: {payload[:200]}...")
                return True
            else:
                self.logger.error(f"D2数据发布失败，错误代码: {result.rc}")
                return False
                
        except Exception as e:
            self.logger.error(f"发布D2数据时发生错误: {e}")
            return False
        
    def publish_d3_data(self, data_file: str = "data/d3_parsed_data.json") -> bool:
        """
        发布D3数据到MQTT
        
        Args:
            data_file: D3数据文件路径
            
        Returns:
            发布是否成功
        """
        if not self.connected:
            self.logger.error("MQTT未连接，无法发布D3消息")
            return False
        
        # 加载D3数据
        d3_data = self.load_d3_data(data_file)
        if not d3_data:
            return False
        
        # 创建D3消息体
        message = self.create_d3_mqtt_message(d3_data)
        
        try:
            # 发布D3数据到指定主题
            topic = "/195/D19DQ66713XIJ/property/post"
            payload = json.dumps(message, ensure_ascii=False, indent=2)
            
            result = self.client.publish(topic, payload, qos=1)
            
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                self.logger.info(f"D3数据发布成功到主题: {topic}")
                self.logger.debug(f"D3消息内容: {payload[:200]}...")
                return True
            else:
                self.logger.error(f"D3数据发布失败，错误代码: {result.rc}")
                return False
                
        except Exception as e:
            self.logger.error(f"发布D3数据时发生错误: {e}")
            return False
            
    def publish_d4_data(self, data_file: str = "data/d4_parsed_data.json") -> bool:
        """
        发布D4数据到MQTT
        
        Args:
            data_file: D4数据文件路径
            
        Returns:
            发布是否成功
        """
        if not self.connected:
            self.logger.error("MQTT未连接，无法发布D4消息")
            return False
        
        # 加载D4数据
        d4_data = self.load_d4_data(data_file)
        if not d4_data:
            return False
        
        # 创建D4消息体
        message = self.create_d4_mqtt_message(d4_data)
        
        try:
            # 发布D4数据到指定主题
            topic = "/195/D19DQ66713XIJ/property/post"
            payload = json.dumps(message, ensure_ascii=False, indent=2)
            
            result = self.client.publish(topic, payload, qos=1)
            
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                self.logger.info(f"D4数据发布成功到主题: {topic}")
                self.logger.debug(f"D4消息内容: {payload[:200]}...")
                return True
            else:
                self.logger.error(f"D4数据发布失败，错误代码: {result.rc}")
                return False
                
        except Exception as e:
            self.logger.error(f"发布D4数据时发生错误: {e}")
            return False
            
    def publish_d5_data(self, data_file: str = "data/d5_parsed_data.json") -> bool:
        """
        发布D5数据到MQTT

        Args:
            data_file: D5数据文件路径

        Returns:
            发布是否成功
        """
        if not self.connected:
            self.logger.error("MQTT未连接，无法发布D5消息")
            return False

        # 加载D5数据
        d5_data = self.load_d5_data(data_file)
        if not d5_data:
            return False

        # 创建D5消息体
        message = self.create_d5_mqtt_message(d5_data)

        try:
            # 发布D5数据到指定主题
            topic = "/195/D19DQ66713XIJ/property/post"
            payload = json.dumps(message, ensure_ascii=False, indent=2)

            result = self.client.publish(topic, payload, qos=1)

            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                self.logger.info(f"D5数据发布成功到主题: {topic}")
                self.logger.debug(f"D5消息内容: {payload[:200]}...")
                return True
            else:
                self.logger.error(f"D5数据发布失败，错误代码: {result.rc}")
                return False

        except Exception as e:
            self.logger.error(f"发布D5数据时发生错误: {e}")
            return False

    def publish_all_types_data(self, c0_file: str = "data/c0_parsed_data.json", 
                              c5_file: str = "data/c5_parsed_data.json",
                              d0_file: str = "data/d0_parsed_data.json",
                              d1_file: str = "data/d1_parsed_data.json",
                              d2_file: str = "data/d2_parsed_data.json",
                              d3_file: str = "data/d3_parsed_data.json",
                              d4_file: str = "data/d4_parsed_data.json",
                              d5_file: str = "data/d5_parsed_data.json",
                              d6_file: str = "data/d6_parsed_data.json",
                              d7_file: str = "data/d7_parsed_data.json",
                              d8_file: str = "data/d8_parsed_data.json") -> bool:
        """
        同时发布所有类型数据（C0、C5、D0、D1、D2、D3、D4、D5、D6、D7、D8）
        
        Args:
            c0_file: C0数据文件路径
            c5_file: C5数据文件路径
            d0_file: D0数据文件路径
            d1_file: D1数据文件路径
            d2_file: D2数据文件路径
            d3_file: D3数据文件路径
            d4_file: D4数据文件路径
            d5_file: D5数据文件路径
            d6_file: D6数据文件路径
            d7_file: D7数据文件路径
            d8_file: D8数据文件路径
            
        Returns:
            发布是否成功
        """
        success_c0 = self.publish_c0_data(c0_file)
        success_c5 = self.publish_c5_data(c5_file)
        success_d0 = self.publish_d0_data(d0_file)
        success_d1 = self.publish_d1_data(d1_file)
        success_d2 = self.publish_d2_data(d2_file)
        success_d3 = self.publish_d3_data(d3_file)
        success_d4 = self.publish_d4_data(d4_file)
        success_d5 = self.publish_d5_data(d5_file)
        success_d6 = self.publish_d6_data(d6_file)
        success_d7 = self.publish_d7_data(d7_file)
        success_d8 = self.publish_d8_data(d8_file)
        
        return success_c0 and success_c5 and success_d0 and success_d1 and success_d2 and success_d3 and success_d4 and success_d5 and success_d6 and success_d7 and success_d8

    

    def _schedule_next_publish_all_types(self):
        """
        调度下一次发布（同时发布所有类型数据）
        """
        def publish_task():
            try:
                if self.connected:
                    success = self.publish_all_types_data(
                        getattr(self, 'c0_file', "data/c0_parsed_data.json"),
                        getattr(self, 'c5_file', "data/c5_parsed_data.json"),
                        getattr(self, 'd0_file', "data/d0_parsed_data.json"),
                        getattr(self, 'd1_file', "data/d1_parsed_data.json"),
                        getattr(self, 'd2_file', "data/d2_parsed_data.json"),
                        getattr(self, 'd3_file', "data/d3_parsed_data.json"),
                        getattr(self, 'd4_file', "data/d4_parsed_data.json"),
                        getattr(self, 'd5_file', "data/d5_parsed_data.json"),
                        getattr(self, 'd6_file', "data/d6_parsed_data.json"),
                        getattr(self, 'd7_file', "data/d7_parsed_data.json"),
                        getattr(self, 'd8_file', "data/d8_parsed_data.json")
                    )
                    if success:
                        self.logger.info("定期发布成功（C0+C5+D0+D1+D2+D3+D4+D5+D6+D7+D8）")
                    else:
                        self.logger.warning("定期发布失败（C0+C5+D0+D1+D2+D3+D4+D5+D6+D7+D8）")
                else:
                    self.logger.warning("MQTT未连接，跳过本次发布")
                    # 如果未连接，尝试重连
                    if self.retry_count < self.max_retry_count:
                        self.reconnect()
                        
            except Exception as e:
                self.logger.error(f"定期发布出错: {e}")
            
            # 调度下一次发布
            self._schedule_next_publish_all_types()
        
        # 创建定时器
        self.publish_timer = threading.Timer(self.monitor_interval, publish_task)
        self.publish_timer.daemon = True
        self.publish_timer.start()

    def load_c0_data(self, data_file: str = "data/c0_parsed_data.json") -> Optional[list]:
        """
        加载C0解析数据
        
        Args:
            data_file: C0数据文件路径
            
        Returns:
            C0解析数据列表
        """
        try:
            data_path = Path(data_file)
            if not data_path.exists():
                self.logger.warning(f"C0数据文件不存在: {data_file}")
                return None
            
            with open(data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.logger.info(f"成功加载C0数据，共{len(data)}个数据点")
            return data
            
        except Exception as e:
            self.logger.error(f"加载C0数据失败: {e}")
            return None
    
    def load_c5_data(self, data_file: str = "data/c5_parsed_data.json") -> Optional[list]:
        """
        加载C5解析数据
        
        Args:
            data_file: C5数据文件路径
            
        Returns:
            C5解析数据列表
        """
        try:
            data_path = Path(data_file)
            if not data_path.exists():
                self.logger.warning(f"C5数据文件不存在: {data_file}")
                return None
            
            with open(data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.logger.info(f"成功加载C5数据，共{len(data)}个数据点")
            return data
            
        except Exception as e:
            self.logger.error(f"加载C5数据失败: {e}")
            return None
    
    def create_mqtt_message(self, c0_data: list) -> list:
        """
        创建MQTT消息体
        
        Args:
            c0_data: C0解析数据
            
        Returns:
            MQTT消息体（直接返回c0_data原始格式）
        """
        return c0_data

    def publish_c0_data(self, data_file: str = "data/c0_parsed_data.json") -> bool:
        """
        发布C0数据到MQTT
        
        Args:
            data_file: C0数据文件路径
            
        Returns:
            发布是否成功
        """
        if not self.connected:
            self.logger.error("MQTT未连接，无法发布消息")
            return False
        
        # 加载C0数据
        c0_data = self.load_c0_data(data_file)
        if not c0_data:
            return False
        
        # 创建消息体
        message = self.create_mqtt_message(c0_data)
        
        try:
            # 发布消息
            topic = self.mqtt_config['reportTopic']
            payload = json.dumps(message, ensure_ascii=False, indent=2)
            
            result = self.client.publish(topic, payload, qos=1)
            
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                self.logger.info(f"C0数据发布成功到主题: {topic}")
                self.logger.debug(f"消息内容: {payload[:200]}...")
                return True
            else:
                self.logger.error(f"C0数据发布失败，错误代码: {result.rc}")
                return False
                
        except Exception as e:
            self.logger.error(f"发布C0数据时发生错误: {e}")
            return False
    
    def start_periodic_publish(self, interval: int = 30, data_file: str = "data/c0_parsed_data.json"):
        """
        启动定期发布服务
        参考FastBee_sdk.py的定时器机制
        
        Args:
            interval: 发布间隔（秒）
            data_file: C0数据文件路径
        """
        self.monitor_interval = interval
        self.data_file = data_file
        self.logger.info(f"启动定期发布服务，间隔: {interval}秒")
        
        # 停止之前的定时器（如果存在）
        self.stop_periodic_publish()
        
        # 启动新的定时器
        self._schedule_next_publish()
    
    def _schedule_next_publish(self):
        """
        调度下一次发布
        参考FastBee_sdk.py的定时器调度机制
        """
        def publish_task():
            try:
                if self.connected:
                    success = self.publish_c0_data(getattr(self, 'data_file', "data/c0_parsed_data.json"))
                    if success:
                        self.logger.info("定期发布成功")
                    else:
                        self.logger.warning("定期发布失败")
                else:
                    self.logger.warning("MQTT未连接，跳过本次发布")
                    # 如果未连接，尝试重连
                    if self.retry_count < self.max_retry_count:
                        self.reconnect()
                        
            except Exception as e:
                self.logger.error(f"定期发布出错: {e}")
            
            # 调度下一次发布
            self._schedule_next_publish()
        
        # 创建定时器
        self.publish_timer = threading.Timer(self.monitor_interval, publish_task)
        self.publish_timer.daemon = True
        self.publish_timer.start()
    
    def create_c5_mqtt_message(self, c5_data: list) -> list:
        """
        创建C5 MQTT消息体
        
        Args:
            c5_data: C5解析数据
            
        Returns:
            C5 MQTT消息体
        """
        return c5_data

    def publish_c5_data(self, data_file: str = "data/c5_parsed_data.json") -> bool:
        """
        发布C5数据到MQTT
        
        Args:
            data_file: C5数据文件路径
            
        Returns:
            发布是否成功
        """
        if not self.connected:
            self.logger.error("MQTT未连接，无法发布C5消息")
            return False
        
        # 加载C5数据
        c5_data = self.load_c5_data(data_file)
        if not c5_data:
            return False
        
        # 创建C5消息体
        message = self.create_c5_mqtt_message(c5_data)
        
        try:
            # 从配置中获取C5发布主题（使用C5_D6_D7_D8合并配置）
            try:
                c5_config = self.config_manager.get_channel_config('C5')
                topic = c5_config['reportTopic']
            except ValueError:
                # 如果没有单独的C5配置，使用C5_D6_D7_D8合并配置
                c5_config = self.config_manager.get_channel_config('C5_D6_D7_D8')
                topic = c5_config['reportTopic']
            
            payload = json.dumps(message, ensure_ascii=False, indent=2)
            
            result = self.client.publish(topic, payload, qos=1)
            
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                self.logger.info(f"C5数据发布成功到主题: {topic}")
                self.logger.debug(f"C5消息内容: {payload[:200]}...")
                return True
            else:
                self.logger.error(f"C5数据发布失败，错误代码: {result.rc}")
                return False
                
        except Exception as e:
            self.logger.error(f"发布C5数据时发生错误: {e}")
            return False

    def publish_all_data(self, c0_file: str = "data/c0_parsed_data.json", c5_file: str = "data/c5_parsed_data.json") -> bool:
        """
        同时发布C0和C5数据
        
        Args:
            c0_file: C0数据文件路径
            c5_file: C5数据文件路径
            
        Returns:
            发布是否成功
        """
        success_c0 = self.publish_c0_data(c0_file)
        success_c5 = self.publish_c5_data(c5_file)
        
        return success_c0 and success_c5

    def start_periodic_publish_all(self, interval: int = 30, 
                                       c0_file: str = "data/c0_parsed_data.json", 
                                       c5_file: str = "data/c5_parsed_data.json",
                                       d0_file: str = "data/d0_parsed_data.json",
                                       d1_file: str = "data/d1_parsed_data.json",
                                       d2_file: str = "data/d2_parsed_data.json",
                                       d3_file: str = "data/d3_parsed_data.json",
                                       d4_file: str = "data/d4_parsed_data.json",
                                       d5_file: str = "data/d5_parsed_data.json",
                                       d6_file: str = "data/d6_parsed_data.json",
                                       d7_file: str = "data/d7_parsed_data.json",
                                       d8_file: str = "data/d8_parsed_data.json"):
        """
        启动定期发布服务（同时发布所有类型数据）
        
        Args:
            interval: 发布间隔（秒）
            c0_file: C0数据文件路径
            c5_file: C5数据文件路径
            d0_file: D0数据文件路径
            d1_file: D1数据文件路径
            d2_file: D2数据文件路径
            d3_file: D3数据文件路径
            d4_file: D4数据文件路径
            d5_file: D5数据文件路径
            d6_file: D6数据文件路径
            d7_file: D7数据文件路径
            d8_file: D8数据文件路径
        """
        self.monitor_interval = interval
        self.c0_file = c0_file
        self.c5_file = c5_file
        self.d0_file = d0_file
        self.d1_file = d1_file
        self.d2_file = d2_file
        self.d3_file = d3_file
        self.d4_file = d4_file
        self.d5_file = d5_file
        self.d6_file = d6_file
        self.d7_file = d7_file
        self.d8_file = d8_file
        self.logger.info(f"启动定期发布服务（C0+C5+D0+D1+D2+D3+D4+D5+D6+D7+D8+59），间隔: {interval}秒")
        
        # 停止之前的定时器（如果存在）
        self.stop_periodic_publish()
        
        # 启动新的定时器
        self._schedule_next_publish_all_types()

    def _schedule_next_publish_all(self):
        """
        调度下一次发布（同时发布C0和C5数据）
        """
        def publish_task():
            try:
                if self.connected:
                    success = self.publish_all_data(
                        getattr(self, 'c0_file', "data/c0_parsed_data.json"),
                        getattr(self, 'c5_file', "data/c5_parsed_data.json")
                    )
                    if success:
                        self.logger.info("定期发布成功（C0+C5）")
                    else:
                        self.logger.warning("定期发布失败（C0+C5）")
                else:
                    self.logger.warning("MQTT未连接，跳过本次发布")
                    # 如果未连接，尝试重连
                    if self.retry_count < self.max_retry_count:
                        self.reconnect()
                        
            except Exception as e:
                self.logger.error(f"定期发布出错: {e}")
            
            # 调度下一次发布
            self._schedule_next_publish_all()
        
        # 创建定时器
        self.publish_timer = threading.Timer(self.monitor_interval, publish_task)
        self.publish_timer.daemon = True
        self.publish_timer.start()
    
    def stop_periodic_publish(self):
        """
        停止定期发布服务
        参考FastBee_sdk.py的定时器管理
        """
        if self.publish_timer and self.publish_timer.is_alive():
            self.publish_timer.cancel()
            self.publish_timer = None
            self.logger.info("定期发布服务已停止")

class UniversalMQTTPublisher:
    """
    通用MQTT发布器
    支持多通道数据发布
    """
    
    def __init__(self, channel: str, config_file: str = "../../config/mqtt_config.json"):
        """
        初始化通用MQTT发布器
        
        Args:
            channel: 通道名称 (C0, 59, D6, D7, D8)
            config_file: 配置文件路径
        """
        # 处理相对路径，转换为基于项目根目录的绝对路径
        if not Path(config_file).is_absolute():
            project_root = Path(__file__).parent.parent.parent
            config_file = str(project_root / config_file.lstrip('../'))
        
        self.channel = channel
        self.config_manager = MQTTConfigManager(config_file)
        self.mqtt_config = None
        self.client = None
        self.connected = False
        self.connection_status = -1
        self.max_retry_count = 5
        self.retry_count = 0
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(f"{__name__}_{channel}")
        
        # 加载配置
        self.load_config()
        
        # 初始化MQTT客户端
        self.init_mqtt_client()
    
    def load_config(self):
        """
        加载指定通道的配置
        支持C5_D6_D7_D8合并配置格式
        """
        try:
            # 首先尝试直接获取通道配置
            try:
                self.mqtt_config = self.config_manager.get_channel_config(self.channel)
            except ValueError:
                # 如果直接获取失败，检查是否属于C5_D6_D7_D8合并配置
                if self.channel in ['C5', 'D6', 'D7', 'D8']:
                    self.logger.info(f"通道{self.channel}使用C5_D6_D7_D8合并配置")
                    self.mqtt_config = self.config_manager.get_channel_config('C5_D6_D7_D8')
                else:
                    raise
            
            self.logger.info(f"通道{self.channel}配置加载成功: {self.mqtt_config['host']}:{self.mqtt_config['port']}")
            self.logger.info(f"客户端ID: {self.mqtt_config['clientId']}")
            
        except Exception as e:
            self.logger.error(f"加载通道{self.channel}配置失败: {e}")
            raise
    
    def init_mqtt_client(self):
        """
        初始化MQTT客户端
        """
        try:
            self.client = mqtt.Client(client_id=self.mqtt_config['clientId'])
            self.client.username_pw_set(
                username=self.mqtt_config['username'],
                password=self.mqtt_config['passwd']
            )
            
            self.client.on_connect = self.on_connect
            self.client.on_disconnect = self.on_disconnect
            self.client.on_publish = self.on_publish
            self.client.on_message = self.on_message
            
            self.client.reconnect_delay_set(min_delay=1, max_delay=120)
            self.logger.info(f"通道{self.channel}MQTT客户端初始化成功")
            
        except Exception as e:
            self.logger.error(f"通道{self.channel}MQTT客户端初始化失败: {e}")
            raise
    
    def on_connect(self, client, userdata, flags, rc):
        """
        MQTT连接回调
        """
        if rc == 0:
            self.connected = True
            self.connection_status = 0
            self.retry_count = 0
            self.logger.info(f"通道{self.channel}MQTT连接成功")
            
            subscribe_topic = self.mqtt_config.get('subscribeTopic')
            if subscribe_topic:
                client.subscribe(subscribe_topic, 1)
                self.logger.info(f"通道{self.channel}订阅主题: {subscribe_topic}")
        else:
            self.connected = False
            self.connection_status = rc
            self.logger.error(f"通道{self.channel}连接失败，错误代码: {rc}")
    
    def on_disconnect(self, client, userdata, rc):
        """
        MQTT断开连接回调
        """
        self.connected = False
        if rc != 0:
            self.logger.warning(f"通道{self.channel}意外断开连接")
        else:
            self.logger.info(f"通道{self.channel}正常断开连接")
    
    def on_message(self, client, userdata, msg):
        """
        MQTT消息接收回调
        """
        try:
            topic = msg.topic
            payload = msg.payload.decode('utf-8')
            self.logger.info(f"通道{self.channel}收到控制消息 - 主题: {topic}, 内容: {payload}")
            
            # 根据通道类型处理消息
            if self.channel == '59':
                self._handle_59_message(payload)
            else:
                self._handle_general_message(payload)
                
        except Exception as e:
            self.logger.error(f"通道{self.channel}处理消息时出错: {e}")
    
    def _handle_59_message(self, payload):
        """
        处理59通道的控制消息
        """
        try:
            message_data = json.loads(payload)
            if isinstance(message_data, list):
                for item in message_data:
                    if isinstance(item, dict) and 'id' in item and 'value' in item:
                        self._update_59_write_value(item['id'], item['value'])
            elif isinstance(message_data, dict) and 'id' in message_data and 'value' in message_data:
                self._update_59_write_value(message_data['id'], message_data['value'])
        except json.JSONDecodeError as e:
            self.logger.error(f"解析59通道JSON消息失败: {e}")
    
    def _handle_general_message(self, payload):
        """
        处理其他通道的控制消息
        """
        self.logger.info(f"通道{self.channel}收到控制消息: {payload}")
        # 这里可以根据需要添加其他通道的消息处理逻辑
    
    def _update_59_write_value(self, data_id: str, new_value: str):
        """
        更新59数据文件中的writeValue字段
        """
        try:
            data_file = "../../data/59_parsed_data.json"
            data_path = Path(data_file)
            
            if not data_path.exists():
                self.logger.error(f"59数据文件不存在: {data_file}")
                return
            
            with open(data_path, 'r', encoding='utf-8') as f:
                data_list = json.load(f)
            
            updated = False
            for item in data_list:
                if item.get('id') == data_id:
                    old_value = item.get('writeValue', '')
                    item['writeValue'] = str(new_value)
                    item['ts'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
                    updated = True
                    self.logger.info(f"更新59数据 {data_id}: {old_value} -> {new_value}")
                    break
            
            if updated:
                with open(data_path, 'w', encoding='utf-8') as f:
                    json.dump(data_list, f, ensure_ascii=False, indent=4)
                self.logger.info(f"59数据文件已更新: {data_file}")
            else:
                self.logger.warning(f"未找到ID为 {data_id} 的59数据点")
                
        except Exception as e:
            self.logger.error(f"更新59数据writeValue时出错: {e}")
    
    def on_publish(self, client, userdata, mid):
        """
        MQTT消息发布回调
        """
        self.logger.debug(f"通道{self.channel}消息发布成功，消息ID: {mid}")
    
    def connect(self) -> bool:
        """
        连接到MQTT服务器
        """
        try:
            host = self.mqtt_config['host']
            port = int(self.mqtt_config['port'])
            
            self.logger.info(f"正在连接通道{self.channel}MQTT服务器: {host}:{port}")
            self.client.connect(host, port, 60)
            self.client.loop_start()
            
            timeout = 10
            start_time = time.time()
            while not self.connected and (time.time() - start_time) < timeout:
                time.sleep(0.1)
            
            return self.connected
                
        except Exception as e:
            self.logger.error(f"通道{self.channel}MQTT连接失败: {e}")
            return False
    
    def disconnect(self):
        """
        断开MQTT连接
        """
        try:
            if self.client and self.connected:
                self.client.disconnect()
                self.client.loop_stop()
                self.logger.info(f"通道{self.channel}MQTT连接已断开")
            
            self.connected = False
            self.connection_status = -1
            
        except Exception as e:
            self.logger.error(f"断开通道{self.channel}MQTT连接时出错: {e}")
    
    def publish_data(self, data_files: list = None) -> bool:
        """
        发布数据到MQTT
        
        Args:
            data_files: 数据文件列表，如果为None则使用配置中的默认文件
            
        Returns:
            发布是否成功
        """
        if not self.connected:
            self.logger.error(f"通道{self.channel}MQTT未连接，无法发布消息")
            return False
        
        if data_files is None:
            # 根据通道类型确定要发布的数据文件
            if self.channel in ['C5', 'D6', 'D7', 'D8']:
                # 对于C5_D6_D7_D8合并配置，只发布对应通道的数据文件
                channel_file_map = {
                    'C5': '../../data/c5_parsed_data.json',
                    'D6': '../../data/d6_parsed_data.json',
                    'D7': '../../data/d7_parsed_data.json',
                    'D8': '../../data/d8_parsed_data.json'
                }
                data_files = [channel_file_map[self.channel]]
            else:
                # 其他通道使用配置中的默认文件
                data_files = self.mqtt_config.get('data_files', [])
        
        success_count = 0
        for data_file in data_files:
            if self._publish_single_file(data_file):
                success_count += 1
        
        return success_count > 0
    
    def _publish_single_file(self, data_file: str) -> bool:
        """
        发布单个数据文件
        """
        try:
            data_path = Path(data_file)
            if not data_path.exists():
                self.logger.warning(f"通道{self.channel}数据文件不存在: {data_file}")
                return False
            
            with open(data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 根据数据文件类型选择正确的主题
            if 'c5_parsed_data.json' in data_file:
                # C5数据使用C5通道的配置
                try:
                    c5_config = self.config_manager.get_channel_config('C5')
                    topic = c5_config['reportTopic']
                except Exception as e:
                    self.logger.error(f"获取C5配置失败: {e}，使用当前通道配置")
                    topic = self.mqtt_config.get('reportTopic')
            else:
                # 其他数据使用当前通道的配置
                topic = self.mqtt_config.get('reportTopic')
            
            payload = json.dumps(data, ensure_ascii=False, indent=2)
            
            result = self.client.publish(topic, payload, qos=1)
            
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                self.logger.info(f"通道{self.channel}数据发布成功: {data_file} -> {topic}")
                return True
            else:
                self.logger.error(f"通道{self.channel}数据发布失败，错误代码: {result.rc}")
                return False
                
        except Exception as e:
            self.logger.error(f"通道{self.channel}发布数据时发生错误: {e}")
            return False


def main_59():
    """
    MQTT59数据推送服务主函数
    """
    print("=== MQTT59 数据推送服务 ===")
    publisher = None
    
    try:
        # 创建通用MQTT推送服务实例
        publisher = UniversalMQTTPublisher('59')
        
        # 连接MQTT服务器
        if not publisher.connect():
            print("MQTT59连接失败，程序退出")
            return
        
        print("MQTT59连接成功！")
        print("按 Ctrl+C 停止服务")
        
        # 发布一次59数据测试
        success = publisher.publish_data()
        if success:
            publisher.logger.info("初始59数据发布成功")
        else:
            publisher.logger.warning("初始59数据发布失败")
        
        # 主循环
        publisher.logger.info("MQTT59推送服务已启动，等待控制消息...")
        
        while True:
            try:
                # 每30秒发布一次59数据
                if int(time.time()) % 30 == 0:
                    publisher.publish_data()
                
                time.sleep(1)
                
            except KeyboardInterrupt:
                publisher.logger.info("收到中断信号，正在关闭服务...")
                break
            except Exception as e:
                publisher.logger.error(f"主循环异常: {e}")
                time.sleep(5)
        
    except Exception as e:
        print(f"程序启动异常: {e}")
    
    finally:
        if publisher:
            try:
                publisher.logger.info("正在清理资源...")
                publisher.disconnect()
                publisher.logger.info("程序已安全退出")
            except Exception as e:
                print(f"清理资源时出错: {e}")
        print("MQTT59推送服务已停止")


def main_all_channels():
    """
    启动三个独立通道的MQTT数据推送服务
    通道1: C0
    通道2: 59
    通道3: C5+D6+D7+D8
    """
    print("=== MQTT 三通道数据推送服务 (C0 | 59 | C5+D6+D7+D8) ===")
    
    # 三个独立的发布器实例
    publisher_c0 = None
    publisher_59 = None
    publisher_c5_d6_d7_d8 = None
    
    try:
        # 创建C0通道推送服务实例
        publisher_c0 = MQTTPublisher(channel='C0')
        
        # 创建59通道推送服务实例
        publisher_59 = MQTT59Publisher()
        
        # 创建C5+D6+D7+D8通道推送服务实例
        publisher_c5_d6_d7_d8 = MQTTPublisher(channel='C5_D6_D7_D8')
        
        # 连接MQTT服务器
        print("正在连接C0通道...")
        publisher_c0.connect()
        
        print("正在连接59通道...")
        publisher_59.connect()
        
        print("正在连接C5+D6+D7+D8通道...")
        publisher_c5_d6_d7_d8.connect()
        
        # 等待连接成功
        print("等待MQTT连接...")
        retry_count = 0
        max_wait_time = 30  # 最大等待30秒
        
        while (not publisher_c0.connected or not publisher_59.connected or not publisher_c5_d6_d7_d8.connected) and retry_count < max_wait_time:
            print("-", end=" ", flush=True)
            time.sleep(1)
            retry_count += 1
        
        if not publisher_c0.connected or not publisher_59.connected or not publisher_c5_d6_d7_d8.connected:
            print("\nMQTT连接超时，程序退出")
            return
        
        print("\nMQTT三通道连接成功！")
        print("按 Ctrl+C 停止服务")
        
        # 发布初始数据
        # C0通道
        success_c0 = publisher_c0.publish_c0_data("data/c0_parsed_data.json")
        if success_c0:
            publisher_c0.logger.info("初始C0数据发布成功")
        else:
            publisher_c0.logger.warning("初始C0数据发布失败")
        
        # 59通道
        success_59 = publisher_59.publish_59_data("data/59_parsed_data.json")
        if success_59:
            publisher_59.logger.info("初始59数据发布成功")
        else:
            publisher_59.logger.warning("初始59数据发布失败")
        
        # C5+D6+D7+D8通道
        success_c5 = publisher_c5_d6_d7_d8.publish_c5_data("data/c5_parsed_data.json")
        success_d6 = publisher_c5_d6_d7_d8.publish_d6_data("data/d6_parsed_data.json")
        success_d7 = publisher_c5_d6_d7_d8.publish_d7_data("data/d7_parsed_data.json")
        success_d8 = publisher_c5_d6_d7_d8.publish_d8_data("data/d8_parsed_data.json")
        
        if success_c5:
            publisher_c5_d6_d7_d8.logger.info("初始C5数据发布成功")
        else:
            publisher_c5_d6_d7_d8.logger.warning("初始C5数据发布失败")
            
        if success_d6:
            publisher_c5_d6_d7_d8.logger.info("初始D6数据发布成功")
        else:
            publisher_c5_d6_d7_d8.logger.warning("初始D6数据发布失败")
            
        if success_d7:
            publisher_c5_d6_d7_d8.logger.info("初始D7数据发布成功")
        else:
            publisher_c5_d6_d7_d8.logger.warning("初始D7数据发布失败")
            
        if success_d8:
            publisher_c5_d6_d7_d8.logger.info("初始D8数据发布成功")
        else:
            publisher_c5_d6_d7_d8.logger.warning("初始D8数据发布失败")
        
        # 启动定期发布服务
        # C0通道定期发布
        publisher_c0.start_periodic_publish(interval=30, data_file="data/c0_parsed_data.json")
        publisher_c0.logger.info("启动C0通道定期发布服务，间隔: 30秒")
        
        # C5+D6+D7+D8通道定期发布
        publisher_c5_d6_d7_d8.start_periodic_publish_all(
            interval=30,
            c0_file="data/c0_parsed_data.json",
            c5_file="data/c5_parsed_data.json",
            d0_file="data/d0_parsed_data.json",
            d1_file="data/d1_parsed_data.json",
            d2_file="data/d2_parsed_data.json",
            d3_file="data/d3_parsed_data.json",
            d4_file="data/d4_parsed_data.json",
            d5_file="data/d5_parsed_data.json",
            d6_file="data/d6_parsed_data.json",
            d7_file="data/d7_parsed_data.json",
            d8_file="data/d8_parsed_data.json"
        )
        publisher_c5_d6_d7_d8.logger.info("启动C5+D0+D1+D2+D3+D4+D5+D6+D7+D8通道定期发布服务，间隔: 30秒")
        
        # 59通道定期发布（使用线程）
        def publish_59_periodically():
            while True:
                try:
                    time.sleep(30)  # 30秒间隔
                    if publisher_59.connected:
                        publisher_59.publish_59_data("data/59_parsed_data.json")
                except Exception as e:
                    publisher_59.logger.error(f"59通道定期发布异常: {e}")
        
        # 启动59通道定期发布线程
        import threading
        thread_59 = threading.Thread(target=publish_59_periodically, daemon=True)
        thread_59.start()
        
        # 主循环
        publisher_c0.logger.info("MQTT全通道推送服务已启动，开始主循环")
        
        while True:
            try:
                # 每10秒进行一次健康检查
                if not publisher_c0.health_check():
                    publisher_c0.logger.warning("C0通道健康检查失败，尝试重连...")
                    if publisher_c0.retry_count < publisher_c0.max_retry_count:
                        publisher_c0.reconnect()
                
                if not publisher_c5_d6_d7_d8.health_check():
                    publisher_c5_d6_d7_d8.logger.warning("C5+D6+D7+D8通道健康检查失败，尝试重连...")
                    if publisher_c5_d6_d7_d8.retry_count < publisher_c5_d6_d7_d8.max_retry_count:
                        publisher_c5_d6_d7_d8.reconnect()
                
                # 检查59通道连接状态
                if not publisher_59.connected:
                    publisher_59.logger.warning("59通道连接断开，尝试重连...")
                    publisher_59.reconnect()
                
                # 打印连接状态（每60秒一次）
                if int(time.time()) % 60 == 0:
                    status_c0 = publisher_c0.get_connection_status()
                    status_c5_d6_d7_d8 = publisher_c5_d6_d7_d8.get_connection_status()
                    status_59 = publisher_59.get_connection_status()
                    publisher_c0.logger.info(f"C0通道状态: {status_c0}")
                    publisher_c5_d6_d7_d8.logger.info(f"C5+D6+D7+D8通道状态: {status_c5_d6_d7_d8}")
                    publisher_59.logger.info(f"59通道状态: {status_59}")
                
                time.sleep(10)  # 主线程休眠10秒
                
            except KeyboardInterrupt:
                publisher_c0.logger.info("收到中断信号，正在关闭全通道服务...")
                break
            except Exception as e:
                publisher_c0.logger.error(f"主循环异常: {e}")
                time.sleep(5)  # 异常时等待5秒再继续
        
    except Exception as e:
        print(f"程序启动异常: {e}")
    
    finally:
        # 清理资源
        if publisher_c0:
            try:
                publisher_c0.logger.info("正在清理C0通道资源...")
                publisher_c0.stop_periodic_publish()
                publisher_c0.disconnect()
                publisher_c0.logger.info("C0通道已安全退出")
            except Exception as e:
                print(f"清理C0通道资源时出错: {e}")
        
        if publisher_c5_d6_d7_d8:
            try:
                publisher_c5_d6_d7_d8.logger.info("正在清理C5+D6+D7+D8通道资源...")
                publisher_c5_d6_d7_d8.stop_periodic_publish()
                publisher_c5_d6_d7_d8.disconnect()
                publisher_c5_d6_d7_d8.logger.info("C5+D6+D7+D8通道已安全退出")
            except Exception as e:
                print(f"清理C5+D6+D7+D8通道资源时出错: {e}")
        
        if publisher_59:
            try:
                publisher_59.logger.info("正在清理59通道资源...")
                publisher_59.disconnect()
                publisher_59.logger.info("59通道已安全退出")
            except Exception as e:
                print(f"清理59通道资源时出错: {e}")
        
        print("MQTT全通道推送服务已停止")


def main():
    """
    主函数
    支持C0、C5、D0、D1、D2、D6、D7、D8数据推送服务
    """
    print("=== MQTT C0+C5+D0+D1+D2+D6+D7+D8 数据推送服务 ===")
    publisher = None
    
    try:
        # 创建MQTT推送服务实例
        publisher = MQTTPublisher()
        
        # 连接MQTT服务器
        publisher.connect()
        
        # 等待连接成功
        print("等待MQTT连接...")
        retry_count = 0
        max_wait_time = 30  # 最大等待30秒
        
        while not publisher.connected and retry_count < max_wait_time:
            print("-", end=" ", flush=True)
            time.sleep(1)
            retry_count += 1
        
        if not publisher.connected:
            print("\nMQTT连接超时，程序退出")
            return
        
        print("\nMQTT连接成功！")
        print("按 Ctrl+C 停止服务")
        
        # 发布一次所有类型数据测试
        success_c0 = publisher.publish_c0_data()
        success_c5 = publisher.publish_c5_data()
        success_d0 = publisher.publish_d0_data()
        success_d1 = publisher.publish_d1_data()
        success_d2 = publisher.publish_d2_data()
        success_d6 = publisher.publish_d6_data()
        success_d7 = publisher.publish_d7_data()
        success_d8 = publisher.publish_d8_data()
        
        if success_c0:
            publisher.logger.info("初始C0数据发布成功")
        else:
            publisher.logger.warning("初始C0数据发布失败")
            
        if success_c5:
            publisher.logger.info("初始C5数据发布成功")
        else:
            publisher.logger.warning("初始C5数据发布失败")
            
        if success_d0:
            publisher.logger.info("初始D0数据发布成功")
        else:
            publisher.logger.warning("初始D0数据发布失败")
            
        if success_d1:
            publisher.logger.info("初始D1数据发布成功")
        else:
            publisher.logger.warning("初始D1数据发布失败")
            
        if success_d2:
            publisher.logger.info("初始D2数据发布成功")
        else:
            publisher.logger.warning("初始D2数据发布失败")
            
        if success_d6:
            publisher.logger.info("初始D6数据发布成功")
        else:
            publisher.logger.warning("初始D6数据发布失败")
            
        if success_d7:
            publisher.logger.info("初始D7数据发布成功")
        else:
            publisher.logger.warning("初始D7数据发布失败")
            
        if success_d8:
            publisher.logger.info("初始D8数据发布成功")
        else:
            publisher.logger.warning("初始D8数据发布失败")
        
        # 启动定期发布服务（同时发布所有类型数据）
        publisher.start_periodic_publish_all(interval=30)
        
        # 主循环
        publisher.logger.info("MQTT推送服务已启动，开始主循环")
        
        while True:
            try:
                # 每10秒进行一次健康检查
                if not publisher.health_check():
                    publisher.logger.warning("健康检查失败，尝试重连...")
                    if publisher.retry_count < publisher.max_retry_count:
                        publisher.reconnect()
                
                # 打印连接状态（每60秒一次）
                if int(time.time()) % 60 == 0:
                    status = publisher.get_connection_status()
                    publisher.logger.info(f"连接状态: {status}")
                
                time.sleep(10)  # 主线程休眠10秒
                
            except KeyboardInterrupt:
                publisher.logger.info("收到中断信号，正在关闭服务...")
                break
            except Exception as e:
                publisher.logger.error(f"主循环异常: {e}")
                time.sleep(5)  # 异常时等待5秒再继续
        
    except Exception as e:
        print(f"程序启动异常: {e}")
    
    finally:
        # 清理资源
        if publisher:
            try:
                publisher.logger.info("正在清理资源...")
                publisher.stop_periodic_publish()
                publisher.disconnect()
                publisher.logger.info("程序已安全退出")
            except Exception as e:
                print(f"清理资源时出错: {e}")
        print("MQTT推送服务已停止")

def main_multi_channel():
    """
    多通道MQTT数据推送服务启动函数
    支持通过命令行参数选择不同的通道
    """
    import sys
    
    # 获取命令行参数
    if len(sys.argv) > 1:
        channel = sys.argv[1].upper()
    else:
        # 默认显示帮助信息
        print("=== MQTT多通道数据推送服务 ===")
        print("使用方法:")
        print("  python mqtt_publisher.py C0    # 启动C0通道")
        print("  python mqtt_publisher.py C5    # 启动C5通道")
        print("  python mqtt_publisher.py 59    # 启动59通道")
        print("  python mqtt_publisher.py D6    # 启动D6通道")
        print("  python mqtt_publisher.py D7    # 启动D7通道")
        print("  python mqtt_publisher.py D8    # 启动D8通道")
        print("  python mqtt_publisher.py ALL   # 启动所有通道(原main函数)")
        print("")
        print("可用通道: C0, C5, 59, D6, D7, D8, ALL")
        return
    
    # 验证通道参数
    valid_channels = ['C0', 'C5', '59', 'D6', 'D7', 'D8', 'ALL']
    if channel not in valid_channels:
        print(f"错误: 无效的通道 '{channel}'")
        print(f"可用通道: {', '.join(valid_channels)}")
        return
    
    # 根据通道启动相应的服务
    if channel == '59':
        main_59()
    elif channel == 'ALL':
        main_all_channels()
    else:
        # 使用通用发布器启动单个通道
        print(f"=== MQTT {channel} 数据推送服务 ===")
        publisher = None
        
        try:
            # 创建通用MQTT推送服务实例
            publisher = UniversalMQTTPublisher(channel)
            
            # 连接MQTT服务器
            if not publisher.connect():
                print(f"MQTT {channel} 连接失败，程序退出")
                return
            
            print(f"MQTT {channel} 连接成功！")
            print("按 Ctrl+C 停止服务")
            
            # 发布一次数据测试
            success = publisher.publish_data()
            if success:
                publisher.logger.info(f"初始{channel}数据发布成功")
            else:
                publisher.logger.warning(f"初始{channel}数据发布失败")
            
            # 主循环
            publisher.logger.info(f"MQTT {channel} 推送服务已启动，等待控制消息...")
            
            # 获取发布间隔
            publish_interval = publisher.mqtt_config.get('publish_interval', 30)
            
            while True:
                try:
                    # 定期发布数据
                    if int(time.time()) % publish_interval == 0:
                        publisher.publish_data()
                    
                    time.sleep(1)
                    
                except KeyboardInterrupt:
                    publisher.logger.info("收到中断信号，正在关闭服务...")
                    break
                except Exception as e:
                    publisher.logger.error(f"主循环异常: {e}")
                    time.sleep(5)
            
        except Exception as e:
            print(f"程序启动异常: {e}")
        
        finally:
            if publisher:
                try:
                    publisher.logger.info("正在清理资源...")
                    publisher.disconnect()
                    publisher.logger.info("程序已安全退出")
                except Exception as e:
                    print(f"清理资源时出错: {e}")
            print(f"MQTT {channel} 推送服务已停止")


if __name__ == '__main__':
    main_multi_channel()